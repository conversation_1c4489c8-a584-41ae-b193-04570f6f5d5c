import request from '@/utils/request'

// 查询保单登记再保产品信息列表
export function listDwsPrpProduct(query) {
  return request({
    url: '/dws/prp/product/list',
    method: 'get',
    params: query
  })
}

// 查询保单登记再保产品信息详细
export function getDwsPrpProduct(id) {
  return request({
    url: '/dws/prp/product/' + id,
    method: 'get'
  })
}

// 新增保单登记再保产品信息
export function addDwsPrpProduct(data) {
  return request({
    url: '/dws/prp/product',
    method: 'post',
    data: data
  })
}

// 修改保单登记再保产品信息
export function updateDwsPrpProduct(data) {
  return request({
    url: '/dws/prp/product',
    method: 'put',
    data: data
  })
}

// 删除保单登记再保产品信息
export function delDwsPrpProduct(id) {
  return request({
    url: '/dws/prp/product/' + id,
    method: 'delete'
  })
}
