<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="交易编码" prop="transactionNo">
        <el-input
          v-model="queryParams.transactionNo"
          placeholder="请输入交易编码"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="保险机构代码" prop="companyCode">
        <el-input
          v-model="queryParams.companyCode"
          placeholder="请输入保险机构代码"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="再保险合同号码" prop="reInsuranceContNo">
        <el-input
          v-model="queryParams.reInsuranceContNo"
          placeholder="请输入再保险合同号码"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品编码" prop="productCode">
        <el-input
          v-model="queryParams.productCode"
          placeholder="请输入产品编码"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入产品名称"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="团个性质" prop="gpFlag">
        <el-select
          v-model="queryParams.gpFlag"
          placeholder="请选择团个性质"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.gp_flag"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所属年份" prop="reportYear">
        <el-input
          v-model="queryParams.reportYear"
          placeholder="请输入所属年份"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属月份" prop="reportMonth">
        <el-select
          v-model="queryParams.reportMonth"
          placeholder="请选择所属月份"
          clearable
          style="width: 200px"
        >
          <el-option v-for="month in 12" :key="month" :label="month + '月'" :value="month" />
        </el-select>
      </el-form-item>
      <el-form-item label="数据来源" prop="dataSource">
        <el-select
          v-model="queryParams.dataSource"
          placeholder="请选择数据来源"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.data_source"
            :key="dict.value"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="推送状态" prop="pushStatus">
        <el-select
          v-model="queryParams.pushStatus"
          placeholder="请选择推送状态"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.push_status"
            :key="dict.value"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['dws:prp:product:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['dws:prp:product:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['dws:prp:product:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['dws:prp:product:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['dws:prp:product:import']"
        >导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dwsPrpProductList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="交易编码" align="center" prop="transactionNo" />
      <el-table-column label="保险机构代码" align="center" prop="companyCode" />
      <el-table-column label="再保险合同号码" align="center" prop="reInsuranceContNo" />
      <el-table-column label="再保险合同名称" align="center" prop="reInsuranceContName" show-overflow-tooltip />
      <el-table-column label="产品编码" align="center" prop="productCode" />
      <el-table-column label="产品名称" align="center" prop="productName" show-overflow-tooltip />
      <el-table-column label="团个性质" align="center" prop="gpFlag">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.gp_flag" :value="scope.row.gpFlag"/>
        </template>
      </el-table-column>
      <el-table-column label="所属年份" align="center" prop="reportYear" />
      <el-table-column label="所属月份" align="center" prop="reportMonth" />
      <el-table-column label="数据来源" align="center" prop="dataSource">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.data_source" :value="scope.row.dataSource"/>
        </template>
      </el-table-column>
      <el-table-column label="推送状态" align="center" prop="pushStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.push_status" :value="scope.row.pushStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="推送日期" align="center" prop="pushDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.pushDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="推送人" align="center" prop="pushBy" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['dws:prp:product:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['dws:prp:product:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改保单登记再保产品信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="交易编码" prop="transactionNo">
              <el-input v-model="form.transactionNo" placeholder="请输入交易编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="保险机构代码" prop="companyCode">
              <el-input v-model="form.companyCode" placeholder="请输入保险机构代码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="再保险合同号码" prop="reInsuranceContNo">
              <el-input v-model="form.reInsuranceContNo" placeholder="请输入再保险合同号码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品编码" prop="productCode">
              <el-input v-model="form.productCode" placeholder="请输入产品编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="再保险合同名称" prop="reInsuranceContName">
          <el-input v-model="form.reInsuranceContName" placeholder="请输入再保险合同名称" />
        </el-form-item>
        <el-form-item label="再保险合同简称" prop="reInsuranceContTitle">
          <el-input v-model="form.reInsuranceContTitle" placeholder="请输入再保险合同简称" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="再保险附约主合同号" prop="mainReInsuranceContNo">
              <el-input v-model="form.mainReInsuranceContNo" placeholder="请输入再保险附约主合同号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同附约类型" prop="contOrAmendmentType">
              <el-select v-model="form.contOrAmendmentType" placeholder="请选择合同附约类型">
                <el-option
                  v-for="dict in dict.type.cont_or_amendment_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="产品名称" prop="productName">
          <el-input v-model="form.productName" placeholder="请输入产品名称" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="团个性质" prop="gpFlag">
              <el-select v-model="form.gpFlag" placeholder="请选择团个性质">
                <el-option
                  v-for="dict in dict.type.gp_flag"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="险类代码" prop="productType">
              <el-input v-model="form.productType" placeholder="请输入险类代码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="责任代码" prop="liabilityCode">
              <el-input v-model="form.liabilityCode" placeholder="请输入责任代码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="责任名称" prop="liabilityName">
              <el-input v-model="form.liabilityName" placeholder="请输入责任名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="再保险公司代码" prop="reinsurerCode">
              <el-input v-model="form.reinsurerCode" placeholder="请输入再保险公司代码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="再保险公司名称" prop="reinsurerName">
              <el-input v-model="form.reinsurerName" placeholder="请输入再保险公司名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="再保人参与份额比例" prop="reinsuranceShare">
              <el-input v-model="form.reinsuranceShare" placeholder="请输入再保人参与份额比例" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分保方式" prop="reinsurMode">
              <el-select v-model="form.reinsurMode" placeholder="请选择分保方式">
                <el-option
                  v-for="dict in dict.type.reinsur_mode"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="再保类型" prop="reInsuranceType">
              <el-select v-model="form.reInsuranceType" placeholder="请选择再保类型">
                <el-option
                  v-for="dict in dict.type.re_insurance_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="保险期限类型" prop="termType">
              <el-select v-model="form.termType" placeholder="请选择保险期限类型">
                <el-option
                  v-for="dict in dict.type.term_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="自留额" prop="retentionAmount">
              <el-input v-model="form.retentionAmount" placeholder="请输入自留额" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="自留比例" prop="retentionPercentage">
              <el-input v-model="form.retentionPercentage" placeholder="请输入自留比例" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="分保比例" prop="quotaSharePercentage">
              <el-input v-model="form.quotaSharePercentage" placeholder="请输入分保比例" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据来源" prop="dataSource">
              <el-select v-model="form.dataSource" placeholder="请选择数据来源">
                <el-option
                  v-for="dict in dict.type.data_source"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属年份" prop="reportYear">
              <el-input v-model.number="form.reportYear" placeholder="请输入所属年份" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属月份" prop="reportMonth">
              <el-select v-model="form.reportMonth" placeholder="请选择所属月份">
                <el-option v-for="month in 12" :key="month" :label="month + '月'" :value="month" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="所属账期" prop="accountPeriod">
          <el-input v-model="form.accountPeriod" placeholder="请输入所属账期" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDwsPrpProduct, getDwsPrpProduct, delDwsPrpProduct, addDwsPrpProduct, updateDwsPrpProduct } from "@/api/dws/prp/product";
import { getToken } from "@/utils/auth";

export default {
  name: "DwsPrpProduct",
  dicts: ['gp_flag', 'cont_or_amendment_type', 'reinsur_mode', 're_insurance_type', 'term_type', 'data_source', 'push_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 保单登记再保产品信息表格数据
      dwsPrpProductList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        TransactionNo: null,
        CompanyCode: null,
        ReInsuranceContNo: null,
        ReInsuranceContName: null,
        ReInsuranceContTitle: null,
        MainReInsuranceContNo: null,
        ContOrAmendmentType: null,
        ProductCode: null,
        ProductName: null,
        GPFlag: null,
        ProductType: null,
        LiabilityCode: null,
        LiabilityName: null,
        ReinsurerCode: null,
        ReinsurerName: null,
        ReinsuranceShare: null,
        ReinsurMode: null,
        ReInsuranceType: null,
        TermType: null,
        RetentionAmount: null,
        RetentionPercentage: null,
        QuotaSharePercentage: null,
        ReportYear: null,
        ReportMonth: null,
        AccountPeriod: null,
        DataSource: null,
        PushStatus: null,
        PushDate: null,
        PushBy: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        TransactionNo: [
          { required: true, message: "交易编码不能为空", trigger: "blur" },
          { max: 64, message: "交易编码长度不能超过64个字符", trigger: "blur" }
        ],
        CompanyCode: [
          { required: true, message: "保险机构代码不能为空", trigger: "blur" },
          { max: 64, message: "保险机构代码长度不能超过64个字符", trigger: "blur" }
        ],
        ReInsuranceContNo: [
          { required: true, message: "再保险合同号码不能为空", trigger: "blur" },
          { max: 64, message: "再保险合同号码长度不能超过64个字符", trigger: "blur" }
        ],
        ReInsuranceContName: [
          { required: true, message: "再保险合同名称不能为空", trigger: "blur" },
          { max: 256, message: "再保险合同名称长度不能超过256个字符", trigger: "blur" }
        ],
        ReInsuranceContTitle: [
          { required: true, message: "再保险合同简称不能为空", trigger: "blur" },
          { max: 256, message: "再保险合同简称长度不能超过256个字符", trigger: "blur" }
        ],
        MainReInsuranceContNo: [
          { required: true, message: "再保险附约主合同号不能为空", trigger: "blur" },
          { max: 64, message: "再保险附约主合同号长度不能超过64个字符", trigger: "blur" }
        ],
        ContOrAmendmentType: [
          { required: true, message: "合同附约类型不能为空", trigger: "change" }
        ],
        ProductCode: [
          { required: true, message: "产品编码不能为空", trigger: "blur" },
          { max: 64, message: "产品编码长度不能超过64个字符", trigger: "blur" }
        ],
        ProductName: [
          { required: true, message: "产品名称不能为空", trigger: "blur" },
          { max: 128, message: "产品名称长度不能超过128个字符", trigger: "blur" }
        ],
        GPFlag: [
          { required: true, message: "团个性质不能为空", trigger: "change" }
        ],
        ProductType: [
          { required: true, message: "险类代码不能为空", trigger: "blur" },
          { max: 64, message: "险类代码长度不能超过64个字符", trigger: "blur" }
        ],
        LiabilityCode: [
          { required: true, message: "责任代码不能为空", trigger: "blur" },
          { max: 64, message: "责任代码长度不能超过64个字符", trigger: "blur" }
        ],
        LiabilityName: [
          { required: true, message: "责任名称不能为空", trigger: "blur" },
          { max: 128, message: "责任名称长度不能超过128个字符", trigger: "blur" }
        ],
        ReinsurerCode: [
          { required: true, message: "再保险公司代码不能为空", trigger: "blur" },
          { max: 64, message: "再保险公司代码长度不能超过64个字符", trigger: "blur" }
        ],
        ReinsurerName: [
          { required: true, message: "再保险公司名称不能为空", trigger: "blur" },
          { max: 256, message: "再保险公司名称长度不能超过256个字符", trigger: "blur" }
        ],
        ReinsuranceShare: [
          { required: true, message: "再保人参与份额比例不能为空", trigger: "blur" },
          { max: 32, message: "再保人参与份额比例长度不能超过32个字符", trigger: "blur" }
        ],
        ReinsurMode: [
          { required: true, message: "分保方式不能为空", trigger: "change" }
        ],
        ReInsuranceType: [
          { required: true, message: "再保类型不能为空", trigger: "change" }
        ],
        TermType: [
          { required: true, message: "保险期限类型不能为空", trigger: "change" }
        ],
        RetentionAmount: [
          { required: true, message: "自留额不能为空", trigger: "blur" },
          { max: 32, message: "自留额长度不能超过32个字符", trigger: "blur" }
        ],
        RetentionPercentage: [
          { required: true, message: "自留比例不能为空", trigger: "blur" },
          { max: 32, message: "自留比例长度不能超过32个字符", trigger: "blur" }
        ],
        QuotaSharePercentage: [
          { required: true, message: "分保比例不能为空", trigger: "blur" },
          { max: 32, message: "分保比例长度不能超过32个字符", trigger: "blur" }
        ],
        ReportYear: [
          { required: true, message: "所属年份不能为空", trigger: "blur" },
          { type: 'number', message: "所属年份必须为数字值", trigger: "blur" }
        ],
        ReportMonth: [
          { required: true, message: "所属月份不能为空", trigger: "change" },
          { type: 'number', min: 1, max: 12, message: "所属月份必须在1-12之间", trigger: "change" }
        ],
        AccountPeriod: [
          { required: true, message: "所属账期不能为空", trigger: "blur" },
          { max: 64, message: "所属账期长度不能超过64个字符", trigger: "blur" }
        ],
        DataSource: [
          { required: true, message: "数据来源不能为空", trigger: "change" }
        ]
      },
      // 上传参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/dws/prp/product/import"
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询保单登记再保产品信息列表 */
    getList() {
      this.loading = true;
      listDwsPrpProduct(this.queryParams).then(response => {
        this.dwsPrpProductList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        Id: null,
        TransactionNo: null,
        CompanyCode: null,
        ReInsuranceContNo: null,
        ReInsuranceContName: null,
        ReInsuranceContTitle: null,
        MainReInsuranceContNo: null,
        ContOrAmendmentType: null,
        ProductCode: null,
        ProductName: null,
        GPFlag: null,
        ProductType: null,
        LiabilityCode: null,
        LiabilityName: null,
        ReinsurerCode: null,
        ReinsurerName: null,
        ReinsuranceShare: null,
        ReinsurMode: null,
        ReInsuranceType: null,
        TermType: null,
        RetentionAmount: null,
        RetentionPercentage: null,
        QuotaSharePercentage: null,
        ReportYear: null,
        ReportMonth: null,
        AccountPeriod: null,
        DataSource: null,
        PushStatus: null,
        PushDate: null,
        PushBy: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.Id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加保单登记再保产品信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.Id || this.ids;
      getDwsPrpProduct(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改保单登记再保产品信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.Id != null) {
            updateDwsPrpProduct(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDwsPrpProduct(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除保单登记再保产品信息编号为"' + ids + '"的数据项？').then(function() {
        return delDwsPrpProduct(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('dws/prp/product/export', {
        ...this.queryParams
      }, `prp_product_${new Date().getTime()}.xlsx`);
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "保单登记再保产品信息导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('dws/prp/product/downloadTemplate', {}, `prp_product_template_${new Date().getTime()}.xlsx`);
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>
