<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="reportForm" size="small" :inline="true" v-show="showSearch" label-width="75px">
      <el-form-item label="报表名称" prop="reportName">
        <el-input v-model="queryParams.reportName" placeholder="请输入报表名称" ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getList">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addReport">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="exportReport">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="reportStatisticsQueryLoading" :data="tableList.data">
      <el-table-column align="center" label="序号" width="60">
        <template slot-scope="scope">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="报表名称" align="center" prop="reportName" min-width="260" show-overflow-tooltip />
      <el-table-column label="报表类型" align="center" prop="reportType">
        <template slot-scope="{row}">
          <dict-tag :options="dict.type.reinsurance_report_type" :value="row.reportType"/>
        </template>
      </el-table-column>
      <el-table-column label="数据类型" align="center" prop="reportDataType">
        <template slot-scope="{row}">
          <dict-tag :options="dict.type.reinsurance_report_data_type" :value="row.reportDataType"/>
        </template>
      </el-table-column>
      <el-table-column label="描述" align="center" prop="remark"></el-table-column>
      <el-table-column label="操作人" align="center" prop="status" >
        <template slot-scope="{row}">
          <dict-tag :options="dict.type.sys_user" :value="row.createBy"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" />
      <el-table-column label="修改时间" align="center" prop="updateTime" />
      <el-table-column label="操作" align="center"  fixed="right" width="240">
        <template slot-scope="{row}">
          <!-- <el-button size="mini" type="text" icon="el-icon-view" @click="Detail(row,true)">查看</el-button> -->
          <el-button size="mini" type="text" icon="el-icon-edit" @click="Detail(row,false)">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="deleteReport(row)">删除</el-button>
          <el-button size="mini" type="text" @click="creatbill(row)">账单报表</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="tableList.total > 0"
      :total="tableList.total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog :title="title" :visible.sync="dialogVisible" width="1000px" >
      <el-form :model="form" :rules="rules" ref="form">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="报表名称" label-width="80px" prop="reportName">
              <el-input v-model="form.reportName" :disabled="disabledr" style="width:300px" clearable placeholder="请输入报表名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据类型" label-width="80px" prop="reportDataType">

              <el-select v-model="form.reportDataType" :disabled="disabledr" placeholder="请选择报表数据类型" filterable style="width:300px">
                <el-option v-for="i in dict.type.reinsurance_report_data_type" :label="i.label" :value="+i.value" :key="i.value"></el-option>
              </el-select>

            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="统计方式" label-width="80px" prop="statMethod">
              <el-radio-group v-model="form.statMethod" :disabled="disabledr" >
                <el-radio :label="0">聚合</el-radio>
                <el-radio :label="1">明细</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注" label-width="80px" prop="remark">
              <el-input v-model="form.remark" :disabled="disabledr" style="width:300px" clearable placeholder="请输入备注"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="报表列" label-width="80px" prop="checkedColumnList">
            <el-transfer v-model="form.checkedColumnList" :disabled="disabledr" @change="rightChange($event)" style="width:900px" :props="{key: 'fieldName',label: 'headerName'}" :titles="['源列表字段', '目标字段']" :data="allColumn">
              <div slot-scope="{ option }" class="optionself"> {{ option.headerName }}
                <el-radio-group v-model="option.statLogic" v-show="form.statMethod==0">
                  <el-radio :label="1">分组列</el-radio>
                  <el-radio :label="2">聚合列</el-radio>
                </el-radio-group>
              </div>
            </el-transfer>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirm" v-if="!disabledr">确 定</el-button>
      </div>
    </el-dialog>
    <el-drawer title="账单报表" :visible.sync="drawer" size="80%" wrapperClosable>
      <statistics :templateType="templateType" :reportCode="reportCode" :totalList="totalList"/>  
    </el-drawer>
  </div>
</template>
<script>
import {delReport,listReport,addReport,getReport,updateReport,columnReport} from "@/api/reinsurance/reinsuranceReport";
import statistics from './statistics.vue'
export default {
  dicts: ['reinsurance_report_type', 'reinsurance_report_data_type', 'business_report_status', 'sys_user'],
  components: {
    statistics
  },
  data() {
    return {
      title:'',
      disabledr:false,
      //显示搜索条件
      showSearch: true,
      //报表查询loading
      reportStatisticsQueryLoading: false,
      dialogVisible: false,
      //报表统计参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        reportType: null,
        billingDate: null
      },
      //报表统计loading
      //表格数据
      tableList: {
        data: [],
        total: 0
      },
      form:{
        reportName:"",
        remark:"",
        reportDataType:"",
        checkedColumnList:[],
        "status": 0,
        statMethod:1,
      },
      rules:{
        reportName: [{ required: true, message: "报表名称不能为空", trigger: "blur" }],
        reportDataType: [{ required: true, message: "报表数据类型不能为空", trigger: "change" }],
        statMethod: [{ required: true, message: "统计方式不能为空", trigger: "change" }],
        checkedColumnList: [{ required: true, message: "报表列不能为空", trigger: "change" }],
      },
      allColumn:[],
      totalList:[],
      drawer:false,
      templateType:'',
      reportCode:'',
    }
  },
  created() {
    this.getList()
    this.getListAll()
    this.getcolumnReport()
  },
  methods: {

    //报表记录查询
    getList() {
      this.reportStatisticsQueryLoading = true
      listReport(this.queryParams).then(res => {
        this.tableList.data = res.rows
        this.tableList.total = res.total
      }).finally(() => {
        this.reportStatisticsQueryLoading = false
      })
    },
    getListAll(bol){
      listReport({pageNum:1,pageSize:9999}).then(res => {
        this.totalList = res.rows
        this.drawer = bol
      })
    },
    getcolumnReport() {
      columnReport().then(res=>{
        this.allColumn = res.data
      })
    },
    addReport(){
      this.resetForm("form");
      this.title = '新增报表模版'
      this.dialogVisible = true
      this.disabledr = false
    },
    confirm(){
      this.$refs["form"].validate(valid => {
        if (valid) {
          let data = JSON.parse(JSON.stringify(this.form))
          data.reportType = -1
          let list = []
          let flag = false
          this.form.checkedColumnList.forEach(element => {
            let i = this.allColumn.filter(item=>{
              return item.fieldName == element
            })
            list = list.concat(i)
          });
          list.forEach((element,index) => {
            element.showOrder = index+=1
            if(data.statMethod == 0 ){
              if(!element.statLogic){
                flag = true
              }
            }else{
              element.statLogic=0
            }
          });
          if(flag){
            this.$message.error('目标字段统计方式需选择分组列或明细列')
            return
          }
          data.checkedColumnList = list
          if(data.id){
            updateReport(data).then(res=>{
              this.$message.success('修改报表模版成功')
              this.getList()
              this.dialogVisible = false
            })
          }else{
            addReport(data).then(res=>{
              this.$message.success('新增报表模版成功')
              this.getList()
              this.dialogVisible = false
            })
          }
        }
      });
    },
    dialogBeforeClose(){

    },
    creatbill(row){
      this.getListAll(true)
      this.templateType = -1
      this.reportCode = row.reportCode
      
    },
    //重置按钮操作
    resetQuery() {
      this.resetForm("reportForm");
      this.handleQuery();
    },
    //搜索按钮操作
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    //下载
    downloadReport(row) {
      window.open(row.reportDataPath, '_blank')
    },
    Detail(row,bol){
      this.disabledr = bol
      this.title = bol?'查看报表模版':'修改报表模版'
      this.dialogVisible = true
      getReport(row.id).then(res=>{
        this.form = res.data
        let list = []
        res.data.checkedColumnList.forEach(i=>{
          list.push(i.fieldName)
           this.allColumn.forEach(ii=>{
            if(ii.fieldName == i.fieldName){
              ii.statLogic = i.statLogic
            }
          })
        })
        this.form.checkedColumnList = list
      })
    },
    //删除
    deleteReport(row) {
      let $this = this
      this.$modal.confirm('是否确认删除？')
        .then(function() {
          delReport(row.id).then(res => {
            $this.$message.success('删除成功')
            $this.handleQuery()
          })
        }).catch(e => console.info(e))
    },
    //导出
    exportReport() {
      this.download('huida-reinsurance/reinsurance/businessReport/export', {
        ...this.queryParams
      }, `业务报表统计记录_${new Date().getTime()}.xlsx`)
    },
    rightChange($event){
      console.log($event)
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep .el-upload {
  width: 100%;
  padding-left: 20px;
  padding-right: 20px;
}
::v-deep .el-transfer-panel {
  width: 340px;
}
::v-deep .optionself {
  width: 254px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .el-radio-group{
    .el-radio{
      margin-right: 5px;
      .el-radio__inner{
        width: 12px;
        height: 12px;
      }
      .el-radio__label{
        font-size: 12px;
        padding-left: 3px;
      }
    }
  }
}
::v-deep .el-upload-dragger {
  width: 100%;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  padding: 20px 20px 0 0;
}
</style>
