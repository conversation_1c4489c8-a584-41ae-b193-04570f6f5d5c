<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="监管报表类型" prop="typeCode">
        <el-select
          v-model="queryParams.typeCode"
          placeholder="监管报表类型"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.regulator_report_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="报表名称" prop="reportCode">
        <el-select
          v-model="queryParams.reportCode"
          placeholder="报表名称"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in queryParams.typeCode === undefined
              ? []
              : dict.type['regulator_report_type_' + queryParams.typeCode]"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="推送状态" prop="pushStatus">
        <el-select
          v-model="queryParams.pushStatus"
          placeholder="推送状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.regulator_report_annual_push_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="报表年份" prop="reportYear">
        <el-date-picker
          v-model="queryParams.reportYear"
          style="width: 240px"
          value-format="yyyy"
          type="year"
          placeholder="报表年份"
          clearable
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="报表月份" prop="reportMonth">
        <el-select
          v-model="queryParams.reportMonth"
          placeholder="报表月份"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="item in 12"
            :key="item"
            :label="item + '月'"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="账单类型" prop="billType">
        <el-select
          v-model="queryParams.billType"
          placeholder="账单类型"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.regulator_bill_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="small"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="small" @click="resetQuery"
          >重置</el-button
        >
        <el-button type="primary" size="small" @click="handleCreate"
          >生成报表数据
        </el-button>
      </el-form-item>
    </el-form>

    <div style="margin-bottom: 10px">
      <el-button
        type="warning"
        plain
        icon="el-icon-download"
        size="small"
        @click="handleExport"
        >导出</el-button
      >
    </div>

    <el-table v-loading="loading" :data="reportList">
      <el-table-column label="序号" align="center" type="index" width="80">
        <template slot-scope="scope">
          <span>{{
            queryParams.pageSize * (queryParams.pageNum - 1) + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="监管报表类型" align="center" prop="typeName" />
      <el-table-column label="报表名称" align="center" prop="reportName" />
      <el-table-column label="推送状态" align="center" prop="pushStatus">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.regulator_report_annual_push_status"
            :value="scope.row.pushStatus"
          />
        </template>
      </el-table-column>
      <el-table-column label="报表年份" align="center" prop="reportYear" />
      <el-table-column label="操作人" align="center" prop="createBy" />
      <el-table-column label="操作" align="center" width="80px">
        <template slot-scope="scope">
          <el-button type="text" @click="handleDetail(scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  listReport,
  createData,
  checkData,
} from "@/api/reinsurance/regulatoryReport.js";

export default {
  name: "RegulatoryReport",
  dicts: [
    "regulator_report_type",
    "regulator_report_type_2",
    "regulator_report_annual_push_status",
    "regulator_bill_type",
  ],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 字典表格数据
      reportList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        typeCode: undefined,
        reportCode: undefined,
        pushStatus: undefined,
        reportYear: undefined,
        reportMonth: undefined,
        billType: undefined,
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleCreate() {
      if (!this.queryParams.typeCode) {
        this.$message.error("请选择监管报表类型");
        return;
      }
      if (!this.queryParams.reportCode) {
        this.$message.error("请选择报表名称");
        return;
      }
      if (!this.queryParams.reportYear) {
        this.$message.error("请选择报表年份");
        return;
      }
      if (!this.queryParams.reportMonth) {
        this.$message.error("请选择报表月份");
        return;
      }
      checkData(this.queryParams).then((response) => {
        if (response.data > 0) {
          this.$confirm("报表已存在，是否继续生成新的报表?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            createData(this.queryParams).then((response) => {
              this.$message.success(response.msg);
              this.getList();
            });
          });
        } else {
          createData(this.queryParams).then((response) => {
            this.$message.success(response.msg);
            this.getList();
          });
        }
      });
    },
    /** 查询字典类型列表 */
    getList() {
      this.loading = true;
      listReport(this.queryParams).then((response) => {
        this.reportList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleExport() {
      this.download(
        "huida-reinsurance/regulatory/report/export",
        {
          ...this.queryParams,
        },
        `监管报表_${new Date().getTime()}.xlsx`
      );
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleDetail(data) {
      let url = "";
      switch (data.reportCode) {
        case 0:
          url = "/report/contractInformation?reportYear=" + data.reportYear;
          break;
        case 1:
          url = "/report/productInformation?reportYear=" + data.reportYear;
          break;
        case 2:
          url = "/report/billInformation?reportYear=" + data.reportYear;
          break;
      }
      this.$router.push(url);
    },
  },
};
</script>
