package com.reinsurance.mapper;

import java.util.List;
import com.reinsurance.domain.LiabilityFormulaApplyEntity;
import com.reinsurance.query.LiabilityFormulaApplyQuery;

/**
 * 险种公式申请Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
public interface LiabilityFormulaApplyMapper 
{
    /**
     * 查询险种公式申请
     * 
     * @param id 险种公式申请主键
     * @return 险种公式申请
     */
    public LiabilityFormulaApplyEntity selectLiabilityFormulaApplyById(Long id);

    /**
     * 查询险种公式申请列表
     * 
     * @param liabilityFormulaApplyQuery 险种公式申请
     * @return 险种公式申请集合
     */
    public List<LiabilityFormulaApplyEntity> selectLiabilityFormulaApplyList(LiabilityFormulaApplyQuery liabilityFormulaApplyQuery);

    /**
     * 新增险种公式申请
     * 
     * @param liabilityFormulaApply 险种公式申请
     * @return 结果
     */
    public int insertLiabilityFormulaApply(LiabilityFormulaApplyEntity liabilityFormulaApply);

    /**
     * 修改险种公式申请
     * 
     * @param liabilityFormulaApply 险种公式申请
     * @return 结果
     */
    public int updateLiabilityFormulaApply(LiabilityFormulaApplyEntity liabilityFormulaApply);

    /**
     * 删除险种公式申请
     * 
     * @param id 险种公式申请主键
     * @return 结果
     */
    public int deleteLiabilityFormulaApplyById(Long id);

    /**
     * 批量删除险种公式申请
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLiabilityFormulaApplyByIds(Long[] ids);
}
