package com.reinsurance.mapper;

import java.util.List;
import com.reinsurance.domain.RiskLiabilityTrackEntity;
import com.reinsurance.query.RiskLiabilityTrackQuery;

/**
 * 险种责任轨迹Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
public interface RiskLiabilityTrackMapper 
{
    /**
     * 查询险种责任轨迹
     * 
     * @param trackId 险种责任轨迹主键
     * @return 险种责任轨迹
     */
    public RiskLiabilityTrackEntity selectRiskLiabilityTrackByTrackId(Long trackId);

    /**
     * 查询险种责任轨迹列表
     * 
     * @param riskLiabilityTrackQuery 险种责任轨迹
     * @return 险种责任轨迹集合
     */
    public List<RiskLiabilityTrackEntity> selectRiskLiabilityTrackList(RiskLiabilityTrackQuery riskLiabilityTrackQuery);

    /**
     * 新增险种责任轨迹
     * 
     * @param riskLiabilityTrack 险种责任轨迹
     * @return 结果
     */
    public int insertRiskLiabilityTrack(RiskLiabilityTrackEntity riskLiabilityTrack);

    /**
     * 修改险种责任轨迹
     * 
     * @param riskLiabilityTrack 险种责任轨迹
     * @return 结果
     */
    public int updateRiskLiabilityTrack(RiskLiabilityTrackEntity riskLiabilityTrack);

    /**
     * 删除险种责任轨迹
     * 
     * @param trackId 险种责任轨迹主键
     * @return 结果
     */
    public int deleteRiskLiabilityTrackByTrackId(Long trackId);

    /**
     * 批量删除险种责任轨迹
     * 
     * @param trackIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRiskLiabilityTrackByTrackIds(Long[] trackIds);
}
