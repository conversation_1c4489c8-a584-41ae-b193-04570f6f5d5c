package com.reinsurance.mapper;

import com.reinsurance.query.DwsPrpProductQuery;
import java.util.List;

/**
 * 保单登记再保产品信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
public interface DwsPrpProductMapper {
    
    /**
     * 查询保单登记再保产品信息
     *
     * @param id 保单登记再保产品信息主键
     * @return 保单登记再保产品信息
     */
    DwsPrpProductEntity selectDwsPrpProductById(Long id);

    /**
     * 查询保单登记再保产品信息列表
     *
     * @param dwsPrpProductQuery 保单登记再保产品信息
     * @return 保单登记再保产品信息集合
     */
    List<DwsPrpProductEntity> selectDwsPrpProductList(DwsPrpProductQuery dwsPrpProductQuery);

    /**
     * 新增保单登记再保产品信息
     *
     * @param dwsPrpProductEntity 保单登记再保产品信息
     * @return 结果
     */
    int insertDwsPrpProduct(DwsPrpProductEntity dwsPrpProductEntity);

    /**
     * 修改保单登记再保产品信息
     *
     * @param dwsPrpProductEntity 保单登记再保产品信息
     * @return 结果
     */
    int updateDwsPrpProduct(DwsPrpProductEntity dwsPrpProductEntity);

    /**
     * 删除保单登记再保产品信息
     *
     * @param id 保单登记再保产品信息主键
     * @return 结果
     */
    int deleteDwsPrpProductById(Long id);

    /**
     * 批量删除保单登记再保产品信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteDwsPrpProductByIds(Long[] ids);

    /**
     * 批量新增保单登记再保产品信息
     *
     * @param dwsPrpProductList 保单登记再保产品信息列表
     * @return 结果
     */
    int insertBatchDwsPrpProduct(List<DwsPrpProductEntity> dwsPrpProductList);
}
