package com.reinsurance.mapper;

import java.util.List;
import com.reinsurance.domain.CedeoutProgrammeRateTrackEntity;
import com.reinsurance.query.CedeoutProgrammeRateTrackQuery;

/**
 * 再保分出方案费率轨迹Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
public interface CedeoutProgrammeRateTrackMapper 
{
    /**
     * 查询再保分出方案费率轨迹
     * 
     * @param trackId 再保分出方案费率轨迹主键
     * @return 再保分出方案费率轨迹
     */
    public CedeoutProgrammeRateTrackEntity selectCedeoutProgrammeRateTrackByTrackId(Long trackId);

    /**
     * 查询再保分出方案费率轨迹列表
     * 
     * @param cedeoutProgrammeRateTrackQuery 再保分出方案费率轨迹
     * @return 再保分出方案费率轨迹集合
     */
    public List<CedeoutProgrammeRateTrackEntity> selectCedeoutProgrammeRateTrackList(CedeoutProgrammeRateTrackQuery cedeoutProgrammeRateTrackQuery);

    /**
     * 新增再保分出方案费率轨迹
     * 
     * @param cedeoutProgrammeRateTrack 再保分出方案费率轨迹
     * @return 结果
     */
    public int insertCedeoutProgrammeRateTrack(CedeoutProgrammeRateTrackEntity cedeoutProgrammeRateTrack);

    /**
     * 修改再保分出方案费率轨迹
     * 
     * @param cedeoutProgrammeRateTrack 再保分出方案费率轨迹
     * @return 结果
     */
    public int updateCedeoutProgrammeRateTrack(CedeoutProgrammeRateTrackEntity cedeoutProgrammeRateTrack);

    /**
     * 删除再保分出方案费率轨迹
     * 
     * @param trackId 再保分出方案费率轨迹主键
     * @return 结果
     */
    public int deleteCedeoutProgrammeRateTrackByTrackId(Long trackId);

    /**
     * 批量删除再保分出方案费率轨迹
     * 
     * @param trackIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCedeoutProgrammeRateTrackByTrackIds(Long[] trackIds);
}
