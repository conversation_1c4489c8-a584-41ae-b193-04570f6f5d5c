package com.reinsurance.domain;

import com.jd.lightning.common.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 分保责任轨迹对象 t_cedeout_liability_track
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class CedeoutLiabilityTrackEntity extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 轨迹Id */
    private Long trackId;

    /** 业务表Id */
    private Long id;

    /** 批次号 */
    private String batchNo;

    /** 版本号 */
    private Long version;

    /** 累计风险编码 */
    private String addupRiskCode;

    /** 累计风险名称 */
    private String addupRiskName;
    
    /** 最大自留额标准 */
    private BigDecimal maxSelfAmount;
    
    /** 状态（0=有效,1=无效） */
    private Integer status;

    /** 是否删除（0=未删除,1=已删除） */
    private Integer isDel;

}
