package com.reinsurance.core.config;

import com.jd.finance.common.util.StarRocksConnector;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * StarRocks数据源配置
 * <AUTHOR>
 *
 */
@Configuration
public class StarRocksConfig {
	
    @Value("${spring.datasource.druid.slave.host}")
    private String host;
    
    @Value("${spring.datasource.druid.slave.port}")
    private int port;
    
    @Value("${spring.datasource.druid.slave.database}")
    private String database;
    
    @Value("${spring.datasource.druid.slave.username}")
    private String user;
    
    @Value("${spring.datasource.druid.slave.password}")
    private String passwd;
    
    
    @Bean
    public StarRocksConnector getStarRocksConnector() {
        return new StarRocksConnector(host, port, database, user, passwd);
    }

}