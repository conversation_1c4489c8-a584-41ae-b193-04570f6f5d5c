package com.reinsurance.query;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import com.jd.lightning.common.core.domain.BaseQuery;


/**
 * East再保产品信息对象
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class DwsEastZbcpxxbQuery extends BaseQuery {

	private static final long serialVersionUID = 2097624450154942952L;

	/** 自增主键 */
    private Long id;

    /** 流水号 */
    private String lsh;

    /** 保险机构代码（唯一固定值：000166） */
    private String bxjgdm;

    /** 保险机构名称（唯一固定值：弘康人寿保险股份有限公司） */
    private String bxjgmc;

    /** 再保险合同号码 */
    private String zbxhthm;

    /** 再保险合同名称 */
    private String zbxhtmc;

    /** 合同附约类型（合同,附约） */
    private String htfylx;

    /** 再保险附约主合同号 */
    private String zbxfyzhth;

    /** 险种编码 */
    private String xzbm;

    /** 险种简称 */
    private String xzjc;

    /** 团个性质（个人,团体） */
    private String tgxz;

    /** 险类 */
    private String xl;

    /** 责任代码 */
    private String zrdm;

    /** 责任名称 */
    private String zrmc;

    /** 再保险公司代码 */
    private String zbxgsdm;

    /** 再保险公司名称 */
    private String zbxgsmc;

    /**分保序号*/
    private String fbxh;
    
    /** 分保方式（枚举值：溢额（YRT）；成数（YRT）；成数溢额混合（YRT）；共保；超赔；超额赔付率） */
    private String fbfs;

    /** 保险期限类型 */
    private String bxqxlx;

    /** 自留额 */
    private String zle;

    /** 自留比例 */
    private String zlbl;

    /** 分保比例 */
    private String fbbl;

    /** 再保人参与份额比例 */
    private String zbrcyfebl;

    /** 采集日期 */
    private String cjrq;
    
    /**数据报送批次号*/
    private String sjbspch;
    
    /**管理机构*/
    private String managecom;

    /** 所属年份 */
    private Integer reportYear;
    
    /** 所属月份 */
    private Integer reportMonth;

    /** 所属账期 */
    private String accountPeriod;

    /** 数据来源（0=系统,1=人工） */
    private Integer dataSource;

    /** 推送状态（0=未推送,1=已推送） */
    private Integer pushStatus;

    /** 推送日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date pushDate;

    /** 推送人 */
    private String pushBy;

    /** 是否删除（0=未删除,1=已删除） */
    private Integer isDel;
}
