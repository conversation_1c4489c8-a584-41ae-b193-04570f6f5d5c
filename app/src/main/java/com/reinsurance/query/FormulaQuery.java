package com.reinsurance.query;

import com.jd.lightning.common.core.domain.BaseQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 计算公式对象 t_formula
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
@Data
@ApiModel("公式配置--查询参数")
@EqualsAndHashCode(callSuper=true)
public class FormulaQuery extends BaseQuery
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    private Long id;

    /** 批次号 */
    private String batchNo;

    /** 版本号 */
    private Long version;

    @ApiModelProperty("公式编码")
    private String formulaCode;

    @ApiModelProperty("公式名称")
    private String formulaName;

    @ApiModelProperty("算法大类 1=风险保额计算, 2=理赔摊回计算, 3=其他")
    private Integer formulaBigType;

    @ApiModelProperty("算法类型，字典：formula_type")
    private String formulaType;

    @ApiModelProperty("分保方式，字典：re_cedeout_way")
    private Integer cedeoutWay;

    @ApiModelProperty("分出模式，字典：re_cedeout_mode")
    private Integer cedeoutMode;

    @ApiModelProperty("再保项目，字典：formula_reinsurance_project")
    private Integer reinsuranceProject;

    @ApiModelProperty("交费方式，字典：re_pay_intv")
    private String payIntv;

    @ApiModelProperty("业务类型，字典：risk_liability_business_type")
    private Integer busiType;

    @ApiModelProperty("公式")
    private String formulaValue;

    @ApiModelProperty("公式描述")
    private String formulaDesc;

    @ApiModelProperty("公式中文")
    private String formulaText;

    @ApiModelProperty("公式富文本")
    private String formulaRichText;

    @ApiModelProperty("状态，字典：formula_status")
    private Integer status;

    /** 是否删除 */
    private Integer isDel;

}
