package com.reinsurance.service.impl;

import com.reinsurance.mapper.DwsPrpProductMapper;
import com.reinsurance.query.DwsPrpProductQuery;
import com.reinsurance.service.IDwsPrpProductService;
import com.reinsurance.utils.ReinsuObjectUtil;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.common.utils.SecurityUtils;
import com.jd.lightning.common.utils.StringUtils;
import com.jd.lightning.common.utils.poi.ExcelUtil;
import cn.hutool.core.collection.CollUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.ArrayList;

/**
 * 保单登记再保产品信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Service
public class DwsPrpProductServiceImpl implements IDwsPrpProductService {
    
    @Autowired
    private DwsPrpProductMapper dwsPrpProductMapper;

    /**
     * 查询保单登记再保产品信息
     *
     * @param id 保单登记再保产品信息主键
     * @return 保单登记再保产品信息
     */
    @Override
    public DwsPrpProductDTO selectDwsPrpProductById(Long id) {
        DwsPrpProductEntity dwsPrpProductEntity = dwsPrpProductMapper.selectDwsPrpProductById(id);
        return ReinsuObjectUtil.convertModel(dwsPrpProductEntity, DwsPrpProductDTO.class);
    }

    /**
     * 查询保单登记再保产品信息列表
     *
     * @param dwsPrpProductQuery 保单登记再保产品信息
     * @return 保单登记再保产品信息
     */
    @Override
    public List<DwsPrpProductDTO> selectDwsPrpProductList(DwsPrpProductQuery dwsPrpProductQuery) {
        List<DwsPrpProductEntity> dwsPrpProductEntitys = dwsPrpProductMapper.selectDwsPrpProductList(dwsPrpProductQuery);
        return ReinsuObjectUtil.convertList(dwsPrpProductEntitys, DwsPrpProductDTO.class);
    }

    /**
     * 新增保单登记再保产品信息
     *
     * @param dwsPrpProductDTO 保单登记再保产品信息
     * @return 结果
     */
    @Override
    public int insertDwsPrpProduct(DwsPrpProductDTO dwsPrpProductDTO) {
        DwsPrpProductEntity dwsPrpProductEntity = ReinsuObjectUtil.convertModel(dwsPrpProductDTO, DwsPrpProductEntity.class);
        dwsPrpProductEntity.setCreateBy(SecurityUtils.getUsername());
        dwsPrpProductEntity.setCreateTime(DateUtils.getNowDate());
        dwsPrpProductEntity.setIsDel(0);
        return dwsPrpProductMapper.insertDwsPrpProduct(dwsPrpProductEntity);
    }

    /**
     * 修改保单登记再保产品信息
     *
     * @param dwsPrpProductDTO 保单登记再保产品信息
     * @return 结果
     */
    @Override
    public int updateDwsPrpProduct(DwsPrpProductDTO dwsPrpProductDTO) {
        DwsPrpProductEntity dwsPrpProductEntity = ReinsuObjectUtil.convertModel(dwsPrpProductDTO, DwsPrpProductEntity.class);
        dwsPrpProductEntity.setUpdateBy(SecurityUtils.getUsername());
        dwsPrpProductEntity.setUpdateTime(DateUtils.getNowDate());
        return dwsPrpProductMapper.updateDwsPrpProduct(dwsPrpProductEntity);
    }

    /**
     * 批量删除保单登记再保产品信息
     *
     * @param ids 需要删除的保单登记再保产品信息主键
     * @return 结果
     */
    @Override
    public int deleteDwsPrpProductByIds(Long[] ids) {
        return dwsPrpProductMapper.deleteDwsPrpProductByIds(ids);
    }

    /**
     * 删除保单登记再保产品信息信息
     *
     * @param id 保单登记再保产品信息主键
     * @return 结果
     */
    @Override
    public int deleteDwsPrpProductById(Long id) {
        return dwsPrpProductMapper.deleteDwsPrpProductById(id);
    }

    /**
     * 导入保单登记再保产品信息数据
     *
     * @param file 导入文件
     * @return 结果
     */
    @Override
    public Result importDwsPrpProduct(MultipartFile file) {
        try {
            ExcelUtil<DwsPrpProductDTO> excelUtil = new ExcelUtil<>(DwsPrpProductDTO.class);
            List<DwsPrpProductDTO> dwsPrpProductList = excelUtil.importExcel(file.getInputStream());
            if (CollUtil.isEmpty(dwsPrpProductList)) {
                return Result.error("导入的保单登记再保产品信息数据为空。");
            }
            
            String error = this.checkImportData(dwsPrpProductList);
            if (StringUtils.isNotBlank(error)) {
                return Result.error(error);
            }
            
            // 批量插入数据
            List<DwsPrpProductEntity> entityList = new ArrayList<>();
            for (DwsPrpProductDTO dto : dwsPrpProductList) {
                DwsPrpProductEntity entity = ReinsuObjectUtil.convertModel(dto, DwsPrpProductEntity.class);
                entity.setCreateBy(SecurityUtils.getUsername());
                entity.setCreateTime(DateUtils.getNowDate());
                entity.setIsDel(0);
                entityList.add(entity);
            }
            
            if (CollUtil.isNotEmpty(entityList)) {
                dwsPrpProductMapper.insertBatchDwsPrpProduct(entityList);
            }
            
            return Result.success("导入成功，共导入" + dwsPrpProductList.size() + "条数据");
        } catch (Exception e) {
            return Result.error("导入失败：" + e.getMessage());
        }
    }

    /**
     * 导出保单登记再保产品信息数据
     *
     * @param response 响应对象
     * @param dwsPrpProductQuery 查询条件
     */
    @Override
    public void exportDwsPrpProduct(HttpServletResponse response, DwsPrpProductQuery dwsPrpProductQuery) {
        List<DwsPrpProductDTO> list = this.selectDwsPrpProductList(dwsPrpProductQuery);
        ExcelUtil<DwsPrpProductDTO> util = new ExcelUtil<>(DwsPrpProductDTO.class);
        util.exportExcel(response, list, "保单登记再保产品信息数据");
    }

    /**
     * 下载导入模板
     *
     * @param response 响应对象
     */
    @Override
    public void downloadTemplate(HttpServletResponse response) {
        ExcelUtil<DwsPrpProductDTO> util = new ExcelUtil<>(DwsPrpProductDTO.class);
        util.importTemplateExcel(response, "保单登记再保产品信息模板");
    }

    /**
     * 校验导入数据
     *
     * @param dwsPrpProductList 导入数据列表
     * @return 错误信息
     */
    private String checkImportData(List<DwsPrpProductDTO> dwsPrpProductList) {
        StringBuilder errorMsg = new StringBuilder();
        
        for (int i = 0; i < dwsPrpProductList.size(); i++) {
            DwsPrpProductDTO dto = dwsPrpProductList.get(i);
            int rowNum = i + 2; // Excel行号从2开始（第1行是表头）
            
            if (StringUtils.isBlank(dto.getTransactionNo())) {
                errorMsg.append("第").append(rowNum).append("行交易编码不能为空；");
            }
            if (StringUtils.isBlank(dto.getCompanyCode())) {
                errorMsg.append("第").append(rowNum).append("行保险机构代码不能为空；");
            }
            if (StringUtils.isBlank(dto.getReInsuranceContNo())) {
                errorMsg.append("第").append(rowNum).append("行再保险合同号码不能为空；");
            }
            if (StringUtils.isBlank(dto.getProductCode())) {
                errorMsg.append("第").append(rowNum).append("行产品编码不能为空；");
            }
            if (dto.getReportYear() == null) {
                errorMsg.append("第").append(rowNum).append("行所属年份不能为空；");
            }
            if (dto.getReportMonth() == null) {
                errorMsg.append("第").append(rowNum).append("行所属月份不能为空；");
            }
            if (dto.getDataSource() == null) {
                errorMsg.append("第").append(rowNum).append("行数据来源不能为空；");
            }
        }
        
        return errorMsg.toString();
    }
}
