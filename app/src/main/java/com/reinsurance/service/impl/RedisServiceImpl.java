package com.reinsurance.service.impl;

import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.common.utils.StringUtils;
import com.reinsurance.enums.BasicDataEnums.RedisKeyModule;
import com.reinsurance.service.IRedisService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Service
public class RedisServiceImpl implements IRedisService {

    @Autowired
    private StringRedisTemplate redisTemplate;

	@Override
	public synchronized String getUniqueCode(RedisKeyModule module) {
		StringBuffer pattern = new StringBuffer();
		for(int i=0; i<module.getLength(); i++) {
			pattern.append("0");
		}
		
		Long timeout = null;
		String codeFristValue = null;
		if(module.getRecountMethod() == Calendar.ERA) {
			codeFristValue = "";
		}else if(module.getRecountMethod() == Calendar.YEAR) {
			timeout = 365L;
			codeFristValue = String.valueOf(DateUtil.thisYear());
		}else if(module.getRecountMethod() == Calendar.MONTH) {
			timeout = 31L;
			codeFristValue = DateUtils.dateTime().substring(0, 6);
		}else {
			timeout = 1L;
			codeFristValue = DateUtils.dateTime();
		}
		
		String redisKey = module + ":" + codeFristValue;
		Long seq = module.getInitValue();
		if(redisTemplate.hasKey(redisKey)){
			seq = redisTemplate.opsForValue().increment(redisKey);
        }else {
        	redisTemplate.opsForValue().set(redisKey, String.valueOf(module.getInitValue()));
        	if(timeout != null) {
        		redisTemplate.expire(redisKey, timeout, TimeUnit.DAYS);
        	}
        }
		DecimalFormat decimalFormat = new DecimalFormat(pattern.toString());
		return module.getPrefix() + codeFristValue + decimalFormat.format(seq);
	}

	@Override
	public synchronized String getUniqueCode(String keySuffix, RedisKeyModule module) {
		StringBuffer pattern = new StringBuffer();
		for(int i=0; i<module.getLength(); i++) {
			pattern.append("0");
		}
		
		Long timeout = null;
		String codeFristValue = null;
		if(module.getRecountMethod() == Calendar.ERA) {
			codeFristValue = "";
		}else if(module.getRecountMethod() == Calendar.YEAR) {
			timeout = 365L;
			codeFristValue = String.valueOf(DateUtil.thisYear());
		}else if(module.getRecountMethod() == Calendar.MONTH) {
			timeout = 31L;
			codeFristValue = DateUtils.dateTime().substring(0, 6);
		}else {
			timeout = 1L;
			codeFristValue = DateUtils.dateTime();
		}
		
		String redisKey = module.getCode() + ":" + codeFristValue + (StringUtils.isNotEmpty(keySuffix) ? ":" + keySuffix : "");
		Long seq = module.getInitValue();
		if(redisTemplate.hasKey(redisKey)){
			seq = redisTemplate.opsForValue().increment(redisKey);
        }else {
        	redisTemplate.opsForValue().set(redisKey, String.valueOf(module.getInitValue()));
        	if(timeout != null) {
        		redisTemplate.expire(redisKey, timeout, TimeUnit.DAYS);
        	}
        }
		DecimalFormat decimalFormat = new DecimalFormat(pattern.toString());
		return keySuffix + codeFristValue + decimalFormat.format(seq);
	}
	
	@Override
	public synchronized List<String> getUniqueCodes(RedisKeyModule module, String keySuffix, Integer number) {
		StringBuffer pattern = new StringBuffer();
		for(int i=0; i<module.getLength(); i++) {
			pattern.append("0");
		}
		Long timeout = null;
		String codeFristValue = null;
		if(module.getRecountMethod() == Calendar.ERA) {
			codeFristValue = "";
		}else if(module.getRecountMethod() == Calendar.YEAR) {
			timeout = 365L;
			codeFristValue = String.valueOf(DateUtil.thisYear());
		}else if(module.getRecountMethod() == Calendar.MONTH) {
			timeout = 31L;
			codeFristValue = DateUtils.dateTime().substring(0, 6);
		}else {
			timeout = 1L;
			codeFristValue = DateUtils.dateTime();
		}
		
		String redisKey = module.getCode() + ":" + codeFristValue + (StringUtils.isNotEmpty(keySuffix) ? ":" + keySuffix : "");
		Long seq = module.getInitValue();
		if(redisTemplate.hasKey(redisKey)){
			seq = redisTemplate.opsForValue().increment(redisKey, number);
        }else {
        	seq = module.getInitValue() + number - 1;
        	redisTemplate.opsForValue().set(redisKey, String.valueOf(seq));
        	if(timeout != null) {
        		redisTemplate.expire(redisKey, timeout, TimeUnit.DAYS);
        	}
        }
		List<String> uniqueCodes = new ArrayList<String>();
		DecimalFormat decimalFormat = new DecimalFormat(pattern.toString());
		for(long i = (seq-number+1); i<=seq ; i++) {
			uniqueCodes.add(keySuffix + codeFristValue + decimalFormat.format(i));
		}
		return uniqueCodes;
	}

	@Override
	public boolean deleteByKey(RedisKeyModule module) {
		if(module == null) {
			return false;
		}
		Set<String> keys = redisTemplate.keys(module.getCode() + ":*");
		if(CollUtil.isEmpty(keys)) {
			return false;
		}
		
		boolean deleteResult = false;
		for(String key : keys) {
			deleteResult = redisTemplate.delete(key).booleanValue();
		}
		return deleteResult;
	}
	
}
