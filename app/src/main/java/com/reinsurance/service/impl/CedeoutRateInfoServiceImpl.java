package com.reinsurance.service.impl;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import lombok.extern.slf4j.Slf4j;

import com.jd.lightning.common.utils.SecurityUtils;
import com.reinsurance.enums.BasicDataEnums;
import com.reinsurance.service.ICacheService;
import com.reinsurance.constant.RsConstant;
import com.reinsurance.domain.*;
import com.reinsurance.dto.*;
import com.reinsurance.enums.CedeoutEnums;
import com.reinsurance.mapper.*;
import com.reinsurance.query.*;
import com.reinsurance.service.IRedisService;
import com.reinsurance.utils.CedeoutUtils;
import com.reinsurance.utils.ReinsuJsonUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.reinsurance.utils.ReinsuObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.common.utils.DictUtils;
import com.reinsurance.service.ICedeoutRateInfoService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 费率信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
@Slf4j
@Service
public class CedeoutRateInfoServiceImpl implements ICedeoutRateInfoService 
{
    @Autowired
    private CedeoutRateInfoMapper cedeoutRateInfoMapper;
    
    @Autowired
    private CedeoutRateInfoApplyMapper cedeoutRateInfoApplyMapper;
    
    @Autowired
    private CedeoutRateInfoTrackMapper cedeoutRateInfoTrackMapper;
    
    @Autowired
    private CedeoutRateDataMapper cedeoutRateDataMapper;
    
    @Autowired
    private CedeoutRateDataApplyMapper cedeoutRateDataApplyMapper;
    
    @Autowired
    private CedeoutRateDataTrackMapper cedeoutRateDataTrackMapper;
    @Autowired
    private CedeoutProgrammeRateMapper cedeoutProgrammeRateMapper;

    @Autowired
    private IRedisService redisService;
    
    @Autowired
    private ICacheService cacheService;

    /**
     * 查询费率信息
     * 
     * @param id 费率信息主键
     * @return 费率信息
     */

    @Override
    public CedeoutRateInfoDTO selectCedeoutRateInfoById(Long id) {
        CedeoutRateInfoEntity cedeoutRateInfoEntity = cedeoutRateInfoMapper.selectCedeoutRateInfoById(id);
        return ReinsuObjectUtil.convertModel(cedeoutRateInfoEntity, CedeoutRateInfoDTO.class);
    }
    /**
     * 查询费率信息列表
     * 
     * @param cedeoutRateInfoQuery 费率信息
     * @return 费率信息
     */
    @Override
    public List<CedeoutRateInfoDTO> selectCedeoutRateInfoList(CedeoutRateInfoQuery cedeoutRateInfoQuery)
    {
    	List<CedeoutRateInfoEntity> cedeoutRateInfoEntitys = cedeoutRateInfoMapper.selectCedeoutRateInfoList(cedeoutRateInfoQuery);
        List<CedeoutRateInfoDTO> dtoList = ReinsuObjectUtil.convertList(cedeoutRateInfoEntitys, CedeoutRateInfoDTO.class);
    	return dtoList;
    }

    /**
     * 新增费率信息
     * 
     * @param cedeoutRateInfoDTO 费率信息
     * @return 结果
     */
    @Override
    public int insertCedeoutRateInfo(CedeoutRateInfoDTO cedeoutRateInfoDTO) {
        // 费率编码按规则生成
        String rateCode = redisService.getUniqueCode(BasicDataEnums.RedisKeyModule.RATE);
    	CedeoutRateInfoEntity cedeoutRateInfoEntity = ReinsuObjectUtil.convertModel(cedeoutRateInfoDTO, CedeoutRateInfoEntity.class);
    	cedeoutRateInfoEntity.setRateCode(rateCode);
    	cedeoutRateInfoEntity.setCreateBy(SecurityUtils.getUsername());
    	cedeoutRateInfoEntity.setUpdateBy(SecurityUtils.getUsername());
    	cedeoutRateInfoEntity.setCreateTime(DateUtils.getNowDate());
    	cedeoutRateInfoEntity.setImportStatus(0); //默认未导入
        cedeoutRateInfoEntity.setStatus(CedeoutEnums.状态_初始化.getValue());
        String batchNo = DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN) + RandomUtil.randomNumbers(5);
        cedeoutRateInfoEntity.setBatchNo(batchNo);
        cedeoutRateInfoEntity.setVersion(Long.valueOf(1));
        cedeoutRateInfoEntity.setIsDel(CedeoutEnums.未删除.getValue());
        return cedeoutRateInfoMapper.insertCedeoutRateInfo(cedeoutRateInfoEntity);
    }

    /**
     * 修改费率信息
     * 
     * @param cedeoutRateInfoDTO 费率信息
     * @return 结果
     */
    @Override
    public int updateCedeoutRateInfo(CedeoutRateInfoDTO cedeoutRateInfoDTO) {
    	CedeoutRateInfoEntity cedeoutRateInfoEntity = ReinsuObjectUtil.convertModel(cedeoutRateInfoDTO, CedeoutRateInfoEntity.class);
        cedeoutRateInfoEntity.setUpdateBy(SecurityUtils.getUsername());
        cedeoutRateInfoEntity.setUpdateTime(DateUtils.getNowDate());
        if(cedeoutRateInfoDTO.getStatus() == CedeoutEnums.状态_无效.getValue()){
            List<CedeoutProgrammeRateEntity> list = cedeoutProgrammeRateMapper.selectCedeoutProgrammeRateListByRateCode(cedeoutRateInfoEntity.getRateCode());
            if(!CollectionUtils.isEmpty(list)){
                return -1;
            }
        }
        return cedeoutRateInfoMapper.updateCedeoutRateInfo(cedeoutRateInfoEntity);
    }

    /**
     * 批量删除费率信息
     * 
     * @param ids 需要删除的费率信息主键
     * @return 结果
     */
    @Override
    public int deleteCedeoutRateInfoByIds(Long[] ids)
    {
        return cedeoutRateInfoMapper.deleteCedeoutRateInfoByIds(ids);
    }

    /**
     * 删除费率信息信息
     * 
     * @param id 费率信息主键
     * @return 结果
     */
    @Override
    public int deleteCedeoutRateInfoById(Long id) {
        CedeoutRateInfoEntity entity = cedeoutRateInfoMapper.selectCedeoutRateInfoById(id);
        List<CedeoutProgrammeRateEntity> list = cedeoutProgrammeRateMapper.selectCedeoutProgrammeRateListByRateCode(entity.getRateCode());
        if(!CollectionUtils.isEmpty(list)){
            return -1;
        }
        return cedeoutRateInfoMapper.deleteCedeoutRateInfoById(id);
    }
    
    /**
     * 查询费率信息申请
     * 
     * @param id 费率信息申请主键
     * @return 费率信息申请
     */
    @Override
    public CedeoutRateInfoApplyDTO selectCedeoutRateInfoApplyById(Long id)
    {
    	CedeoutRateInfoApplyEntity cedeoutRateInfoApplyEntity = cedeoutRateInfoApplyMapper.selectCedeoutRateInfoApplyById(id);
    	return ReinsuObjectUtil.convertModel(cedeoutRateInfoApplyEntity, CedeoutRateInfoApplyDTO.class);
    }

    /**
     * 查询费率信息申请列表
     * 
     * @param cedeoutRateInfoApplyQuery 费率信息申请
     * @return 费率信息申请
     */
    @Override
    public List<CedeoutRateInfoApplyDTO> selectCedeoutRateInfoApplyList(CedeoutRateInfoApplyQuery cedeoutRateInfoApplyQuery)
    {
    	List<CedeoutRateInfoApplyEntity> cedeoutRateInfoApplyEntitys = cedeoutRateInfoApplyMapper.selectCedeoutRateInfoApplyList(cedeoutRateInfoApplyQuery);
    	return ReinsuObjectUtil.convertList(cedeoutRateInfoApplyEntitys, CedeoutRateInfoApplyDTO.class);
    }

    /**
     * 新增费率信息申请
     * 
     * @param cedeoutRateInfoApplyDTO 费率信息申请
     * @return 结果
     */
    @Override
    public int insertCedeoutRateInfoApply(CedeoutRateInfoApplyDTO cedeoutRateInfoApplyDTO)
    {
    	CedeoutRateInfoApplyEntity cedeoutRateInfoApplyEntity = ReinsuObjectUtil.convertModel(cedeoutRateInfoApplyDTO, CedeoutRateInfoApplyEntity.class);
    	if(cedeoutRateInfoApplyEntity.getCreateTime() == null) {
    		cedeoutRateInfoApplyEntity.setCreateTime(DateUtils.getNowDate());
    	}
        return cedeoutRateInfoApplyMapper.insertCedeoutRateInfoApply(cedeoutRateInfoApplyEntity);
    }

    /**
     * 修改费率信息申请
     * 
     * @param cedeoutRateInfoApplyDTO 费率信息申请
     * @return 结果
     */
    @Override
    public int updateCedeoutRateInfoApply(CedeoutRateInfoApplyDTO cedeoutRateInfoApplyDTO)
    {
    	CedeoutRateInfoApplyEntity cedeoutRateInfoApplyEntity = ReinsuObjectUtil.convertModel(cedeoutRateInfoApplyDTO, CedeoutRateInfoApplyEntity.class);
    	if(cedeoutRateInfoApplyEntity.getUpdateTime() == null) {
    		cedeoutRateInfoApplyEntity.setUpdateTime(DateUtils.getNowDate());
    	}
        return cedeoutRateInfoApplyMapper.updateCedeoutRateInfoApply(cedeoutRateInfoApplyEntity);
    }

    /**
     * 批量删除费率信息申请
     * 
     * @param ids 需要删除的费率信息申请主键
     * @return 结果
     */
    @Override
    public int deleteCedeoutRateInfoApplyByIds(Long[] ids)
    {
        return cedeoutRateInfoApplyMapper.deleteCedeoutRateInfoApplyByIds(ids);
    }

    /**
     * 删除费率信息申请信息
     * 
     * @param id 费率信息申请主键
     * @return 结果
     */
    @Override
    public int deleteCedeoutRateInfoApplyById(Long id)
    {
        return cedeoutRateInfoApplyMapper.deleteCedeoutRateInfoApplyById(id);
    }
    
    /**
     * 查询费率信息轨迹
     * 
     * @param trackId 费率信息轨迹主键
     * @return 费率信息轨迹
     */
    @Override
    public CedeoutRateInfoTrackDTO selectCedeoutRateInfoTrackByTrackId(Long trackId)
    {
    	CedeoutRateInfoTrackEntity cedeoutRateInfoTrackEntity = cedeoutRateInfoTrackMapper.selectCedeoutRateInfoTrackByTrackId(trackId);
    	return ReinsuObjectUtil.convertModel(cedeoutRateInfoTrackEntity, CedeoutRateInfoTrackDTO.class);
    }

    /**
     * 查询费率信息轨迹列表
     * 
     * @param cedeoutRateInfoTrackQuery 费率信息轨迹
     * @return 费率信息轨迹
     */
    @Override
    public List<CedeoutRateInfoTrackDTO> selectCedeoutRateInfoTrackList(CedeoutRateInfoTrackQuery cedeoutRateInfoTrackQuery)
    {
    	List<CedeoutRateInfoTrackEntity> cedeoutRateInfoTrackEntitys = cedeoutRateInfoTrackMapper.selectCedeoutRateInfoTrackList(cedeoutRateInfoTrackQuery);
    	return ReinsuObjectUtil.convertList(cedeoutRateInfoTrackEntitys, CedeoutRateInfoTrackDTO.class);
    }

    /**
     * 新增费率信息轨迹
     * 
     * @param cedeoutRateInfoTrackDTO 费率信息轨迹
     * @return 结果
     */
    @Override
    public int insertCedeoutRateInfoTrack(CedeoutRateInfoTrackDTO cedeoutRateInfoTrackDTO)
    {
    	CedeoutRateInfoTrackEntity cedeoutRateInfoTrackEntity = ReinsuObjectUtil.convertModel(cedeoutRateInfoTrackDTO, CedeoutRateInfoTrackEntity.class);
    	if(cedeoutRateInfoTrackEntity.getCreateTime() == null) {
    		cedeoutRateInfoTrackEntity.setCreateTime(DateUtils.getNowDate());
    	}
        return cedeoutRateInfoTrackMapper.insertCedeoutRateInfoTrack(cedeoutRateInfoTrackEntity);
    }

    /**
     * 修改费率信息轨迹
     * 
     * @param cedeoutRateInfoTrackDTO 费率信息轨迹
     * @return 结果
     */
    @Override
    public int updateCedeoutRateInfoTrack(CedeoutRateInfoTrackDTO cedeoutRateInfoTrackDTO)
    {
    	CedeoutRateInfoTrackEntity cedeoutRateInfoTrackEntity = ReinsuObjectUtil.convertModel(cedeoutRateInfoTrackDTO, CedeoutRateInfoTrackEntity.class);
    	if(cedeoutRateInfoTrackEntity.getUpdateTime() == null) {
    		cedeoutRateInfoTrackEntity.setUpdateTime(DateUtils.getNowDate());
    	}
        return cedeoutRateInfoTrackMapper.updateCedeoutRateInfoTrack(cedeoutRateInfoTrackEntity);
    }

    /**
     * 批量删除费率信息轨迹
     * 
     * @param trackIds 需要删除的费率信息轨迹主键
     * @return 结果
     */
    @Override
    public int deleteCedeoutRateInfoTrackByTrackIds(Long[] trackIds)
    {
        return cedeoutRateInfoTrackMapper.deleteCedeoutRateInfoTrackByTrackIds(trackIds);
    }

    /**
     * 删除费率信息轨迹信息
     * 
     * @param trackId 费率信息轨迹主键
     * @return 结果
     */
    @Override
    public int deleteCedeoutRateInfoTrackByTrackId(Long trackId)
    {
        return cedeoutRateInfoTrackMapper.deleteCedeoutRateInfoTrackByTrackId(trackId);
    }
    
    /**
     * 查询费率数据
     * 
     * @param cedeoutRateInfoQuery 费率数据主键
     * @return 费率数据
     */
    @Override
    public List<CedeoutRateDataDTO> selectCedeoutRateDataByRateId(CedeoutRateInfoQuery cedeoutRateInfoQuery)
    {
        List<CedeoutRateDataEntity> dataEntityList = cedeoutRateDataMapper.selectCedeoutRateDataListByRateCode(cedeoutRateInfoQuery.getRateCode());
        return ReinsuObjectUtil.convertList(dataEntityList, CedeoutRateDataDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertCedeoutRateDataBatch(List<CedeoutRateDataDTO> cedeoutRateExcelDTOList,String rateCode) {
        CedeoutRateInfoEntity cedeoutRateInfoEntity = cedeoutRateInfoMapper.selectCedeoutRateInfoByRateCode(rateCode);
        String batchNo = DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN) + RandomUtil.randomNumbers(5);
        List<CedeoutRateDataDTO> list = cedeoutRateExcelDTOList.stream()
                .peek(obj -> obj.setBatchNo(batchNo))
                .peek(obj -> obj.setVersion(Long.valueOf(1)))
                .peek(obj -> obj.setRateCode(rateCode))
                .peek(obj -> obj.setRateType(cedeoutRateInfoEntity.getRateType()))
                .peek(obj -> obj.setCreateBy(SecurityUtils.getUsername()))
                .peek(obj -> obj.setCreateTime(DateUtils.getNowDate()))
                .peek(obj -> obj.setIsDel(0))
                .collect(Collectors.toList());
        int n = cedeoutRateDataMapper.insertCedeoutRateDataBatch(list);
        if(n > 0){
            //设置rateInfo表已导入状态
            CedeoutRateInfoEntity entity = new CedeoutRateInfoEntity();
            entity.setId(cedeoutRateInfoEntity.getId());
            entity.setImportStatus(CedeoutEnums.导入状态_已导入.getValue());
            cedeoutRateInfoMapper.updateCedeoutRateInfo(entity);
        }
        return n;
    }

    /**
     * 查询费率数据列表
     * 
     * @param cedeoutRateDataQuery 费率数据
     * @return 费率数据
     */
    @Override
    public List<CedeoutRateDataDTO> selectCedeoutRateDataList(CedeoutRateDataQuery cedeoutRateDataQuery)
    {
    	List<CedeoutRateDataEntity> cedeoutRateDataEntitys = cedeoutRateDataMapper.selectCedeoutRateDataList(cedeoutRateDataQuery);
    	return ReinsuObjectUtil.convertList(cedeoutRateDataEntitys, CedeoutRateDataDTO.class);
    }

    /**
     * 查询费率数据列表
     * 
     * @param cedeoutRateDataQuery 费率数据
     * @return 费率数据
     */
    @Override
    public List<CedeoutRateDataEntity> selectCedeoutRateDataValueList(CedeoutRateDataQuery cedeoutRateDataQuery)
    {
    	return cedeoutRateDataMapper.selectCedeoutRateDataValueList(cedeoutRateDataQuery);
    }
    
    /**
     * 新增费率数据
     * 
     * @param cedeoutRateDataDTO 费率数据
     * @return 结果
     */
    @Override
    public int insertCedeoutRateData(CedeoutRateDataDTO cedeoutRateDataDTO)
    {
    	CedeoutRateDataEntity cedeoutRateDataEntity = ReinsuObjectUtil.convertModel(cedeoutRateDataDTO, CedeoutRateDataEntity.class);
    	if(cedeoutRateDataEntity.getCreateTime() == null) {
    		cedeoutRateDataEntity.setCreateTime(DateUtils.getNowDate());
    	}
        return cedeoutRateDataMapper.insertCedeoutRateData(cedeoutRateDataEntity);
    }

    /**
     * 修改费率数据
     * 
     * @param cedeoutRateDataDTO 费率数据
     * @return 结果
     */
    @Override
    public int updateCedeoutRateData(CedeoutRateDataDTO cedeoutRateDataDTO)
    {
    	CedeoutRateDataEntity cedeoutRateDataEntity = ReinsuObjectUtil.convertModel(cedeoutRateDataDTO, CedeoutRateDataEntity.class);
    	if(cedeoutRateDataEntity.getUpdateTime() == null) {
    		cedeoutRateDataEntity.setUpdateTime(DateUtils.getNowDate());
    	}
        return cedeoutRateDataMapper.updateCedeoutRateData(cedeoutRateDataEntity);
    }

    /**
     * 批量删除费率数据
     * 
     * @param ids 需要删除的费率数据主键
     * @return 结果
     */
    @Override
    public int deleteCedeoutRateDataByIds(Long[] ids)
    {
        return cedeoutRateDataMapper.deleteCedeoutRateDataByIds(ids);
    }

    /**
     * 删除费率数据信息
     * 
     * @param id 费率数据主键
     * @return 结果
     */
    @Override
    public int deleteCedeoutRateDataById(Long id)
    {
        return cedeoutRateDataMapper.deleteCedeoutRateDataById(id);
    }
    
    /**
     * 查询费率数据申请
     * 
     * @param id 费率数据申请主键
     * @return 费率数据申请
     */
    @Override
    public CedeoutRateDataApplyDTO selectCedeoutRateDataApplyById(Long id)
    {
    	CedeoutRateDataApplyEntity cedeoutRateDataApplyEntity = cedeoutRateDataApplyMapper.selectCedeoutRateDataApplyById(id);
    	return ReinsuObjectUtil.convertModel(cedeoutRateDataApplyEntity, CedeoutRateDataApplyDTO.class);
    }

    /**
     * 查询费率数据申请列表
     * 
     * @param cedeoutRateDataApplyQuery 费率数据申请
     * @return 费率数据申请
     */
    @Override
    public List<CedeoutRateDataApplyDTO> selectCedeoutRateDataApplyList(CedeoutRateDataApplyQuery cedeoutRateDataApplyQuery)
    {
    	List<CedeoutRateDataApplyEntity> cedeoutRateDataApplyEntitys = cedeoutRateDataApplyMapper.selectCedeoutRateDataApplyList(cedeoutRateDataApplyQuery);
    	return ReinsuObjectUtil.convertList(cedeoutRateDataApplyEntitys, CedeoutRateDataApplyDTO.class);
    }

    /**
     * 新增费率数据申请
     * 
     * @param cedeoutRateDataApplyDTO 费率数据申请
     * @return 结果
     */
    @Override
    public int insertCedeoutRateDataApply(CedeoutRateDataApplyDTO cedeoutRateDataApplyDTO)
    {
    	CedeoutRateDataApplyEntity cedeoutRateDataApplyEntity = ReinsuObjectUtil.convertModel(cedeoutRateDataApplyDTO, CedeoutRateDataApplyEntity.class);
    	if(cedeoutRateDataApplyEntity.getCreateTime() == null) {
    		cedeoutRateDataApplyEntity.setCreateTime(DateUtils.getNowDate());
    	}
        return cedeoutRateDataApplyMapper.insertCedeoutRateDataApply(cedeoutRateDataApplyEntity);
    }

    /**
     * 修改费率数据申请
     * 
     * @param cedeoutRateDataApplyDTO 费率数据申请
     * @return 结果
     */
    @Override
    public int updateCedeoutRateDataApply(CedeoutRateDataApplyDTO cedeoutRateDataApplyDTO)
    {
    	CedeoutRateDataApplyEntity cedeoutRateDataApplyEntity = ReinsuObjectUtil.convertModel(cedeoutRateDataApplyDTO, CedeoutRateDataApplyEntity.class);
    	if(cedeoutRateDataApplyEntity.getUpdateTime() == null) {
    		cedeoutRateDataApplyEntity.setUpdateTime(DateUtils.getNowDate());
    	}
        return cedeoutRateDataApplyMapper.updateCedeoutRateDataApply(cedeoutRateDataApplyEntity);
    }

    /**
     * 批量删除费率数据申请
     * 
     * @param ids 需要删除的费率数据申请主键
     * @return 结果
     */
    @Override
    public int deleteCedeoutRateDataApplyByIds(Long[] ids)
    {
        return cedeoutRateDataApplyMapper.deleteCedeoutRateDataApplyByIds(ids);
    }

    /**
     * 删除费率数据申请信息
     * 
     * @param id 费率数据申请主键
     * @return 结果
     */
    @Override
    public int deleteCedeoutRateDataApplyById(Long id)
    {
        return cedeoutRateDataApplyMapper.deleteCedeoutRateDataApplyById(id);
    }
    
    /**
     * 查询费率数据轨迹
     * 
     * @param trackId 费率数据轨迹主键
     * @return 费率数据轨迹
     */
    @Override
    public CedeoutRateDataTrackDTO selectCedeoutRateDataTrackByTrackId(Long trackId)
    {
    	CedeoutRateDataTrackEntity cedeoutRateDataTrackEntity = cedeoutRateDataTrackMapper.selectCedeoutRateDataTrackByTrackId(trackId);
    	return ReinsuObjectUtil.convertModel(cedeoutRateDataTrackEntity, CedeoutRateDataTrackDTO.class);
    }

    /**
     * 查询费率数据轨迹列表
     * 
     * @param cedeoutRateDataTrackQuery 费率数据轨迹
     * @return 费率数据轨迹
     */
    @Override
    public List<CedeoutRateDataTrackDTO> selectCedeoutRateDataTrackList(CedeoutRateDataTrackQuery cedeoutRateDataTrackQuery)
    {
    	List<CedeoutRateDataTrackEntity> cedeoutRateDataTrackEntitys = cedeoutRateDataTrackMapper.selectCedeoutRateDataTrackList(cedeoutRateDataTrackQuery);
    	return ReinsuObjectUtil.convertList(cedeoutRateDataTrackEntitys, CedeoutRateDataTrackDTO.class);
    }

    /**
     * 新增费率数据轨迹
     * 
     * @param cedeoutRateDataTrackDTO 费率数据轨迹
     * @return 结果
     */
    @Override
    public int insertCedeoutRateDataTrack(CedeoutRateDataTrackDTO cedeoutRateDataTrackDTO)
    {
    	CedeoutRateDataTrackEntity cedeoutRateDataTrackEntity = ReinsuObjectUtil.convertModel(cedeoutRateDataTrackDTO, CedeoutRateDataTrackEntity.class);
    	if(cedeoutRateDataTrackEntity.getCreateTime() == null) {
    		cedeoutRateDataTrackEntity.setCreateTime(DateUtils.getNowDate());
    	}
        return cedeoutRateDataTrackMapper.insertCedeoutRateDataTrack(cedeoutRateDataTrackEntity);
    }

    /**
     * 修改费率数据轨迹
     * 
     * @param cedeoutRateDataTrackDTO 费率数据轨迹
     * @return 结果
     */
    @Override
    public int updateCedeoutRateDataTrack(CedeoutRateDataTrackDTO cedeoutRateDataTrackDTO)
    {
    	CedeoutRateDataTrackEntity cedeoutRateDataTrackEntity = ReinsuObjectUtil.convertModel(cedeoutRateDataTrackDTO, CedeoutRateDataTrackEntity.class);
    	if(cedeoutRateDataTrackEntity.getUpdateTime() == null) {
    		cedeoutRateDataTrackEntity.setUpdateTime(DateUtils.getNowDate());
    	}
        return cedeoutRateDataTrackMapper.updateCedeoutRateDataTrack(cedeoutRateDataTrackEntity);
    }

    /**
     * 批量删除费率数据轨迹
     * 
     * @param trackIds 需要删除的费率数据轨迹主键
     * @return 结果
     */
    @Override
    public int deleteCedeoutRateDataTrackByTrackIds(Long[] trackIds)
    {
        return cedeoutRateDataTrackMapper.deleteCedeoutRateDataTrackByTrackIds(trackIds);
    }

    /**
     * 删除费率数据轨迹信息
     * 
     * @param trackId 费率数据轨迹主键
     * @return 结果
     */
    @Override
    public int deleteCedeoutRateDataTrackByTrackId(Long trackId)
    {
        return cedeoutRateDataTrackMapper.deleteCedeoutRateDataTrackByTrackId(trackId);
    }
    
	@Override
	public CedeoutRateDataDTO selectCedeoutRateData(Integer rateType, DwsReinsuTradeDTO dwsReinsuTradeDTO) {
		CedeoutRateDataDTO cedeoutRateDataDTO = null;
		CedeoutRateDataQuery cedeoutRateDataQuery = new CedeoutRateDataQuery();
		try {
			cedeoutRateDataQuery.setRateType(rateType);
			if(CedeoutEnums.费率维护_费率.getValue() == rateType) {
				cedeoutRateDataQuery.setRateCode(dwsReinsuTradeDTO.getRateCode());
				cedeoutRateDataQuery.setInsuredSex(dwsReinsuTradeDTO.getInsuredSex() == CedeoutEnums.性别_男.getValue() ? CedeoutEnums.费率_性别_男.getValue() : CedeoutEnums.费率_性别_女.getValue());
				//添加承保类型条件匹配费率
				if(StringUtils.isNotBlank(DictUtils.getDictLabel(RsConstant.rateRiskType, dwsReinsuTradeDTO.getRiskCode()))) {
					cedeoutRateDataQuery.setRiskType(Integer.valueOf(dwsReinsuTradeDTO.getPolRiskType()));
				}
                //费率添加被保险人职业类别的险种编码
                if(StringUtils.isNotBlank(DictUtils.getDictLabel(RsConstant.rateInsuredOccType, dwsReinsuTradeDTO.getRiskCode()))) {
                    cedeoutRateDataQuery.setInsuredOccType(dwsReinsuTradeDTO.getInsuredOccType());
                }
				//添加投保年龄/实际年龄
				if(StringUtils.isNotBlank(DictUtils.getDictLabel(RsConstant.rateInsuredAppAge, dwsReinsuTradeDTO.getRiskCode()))) {
					cedeoutRateDataQuery.setInsuredAppAge(dwsReinsuTradeDTO.getInsuredAppAge());
				}else {
					cedeoutRateDataQuery.setInsuredAge(CedeoutUtils.getContYearWithinArriveAge(dwsReinsuTradeDTO.getInsuredAppAge(), dwsReinsuTradeDTO.getContYear()));
				}
                //费率添加被保险人数量
                cedeoutRateDataQuery.setInsuredPeoples(dwsReinsuTradeDTO.getInsuredPeoples());
				cedeoutRateDataDTO = cacheService.getCedeoutRateData(cedeoutRateDataQuery);
				if(cedeoutRateDataDTO == null) {
					CedeoutRateDataEntity cedeoutRateDataEntity = cedeoutRateDataMapper.selectCedeoutRateData(cedeoutRateDataQuery);
					if(cedeoutRateDataEntity != null) {
						cedeoutRateDataDTO = BeanUtil.toBean(cedeoutRateDataEntity, CedeoutRateDataDTO.class);
						cacheService.setCedeoutRateData(cedeoutRateDataDTO);
					}
				}
			}else if(CedeoutEnums.费率维护_佣金率.getValue() == rateType) {
				cedeoutRateDataQuery.setRateCode(dwsReinsuTradeDTO.getComRateCode());
				cedeoutRateDataQuery.setPolicyYear(dwsReinsuTradeDTO.getContYear());
				cedeoutRateDataDTO = cacheService.getComCedeoutRateData(cedeoutRateDataQuery);
				if(cedeoutRateDataDTO == null) {
					CedeoutRateDataEntity cedeoutRateDataEntity = cedeoutRateDataMapper.selectCedeoutRateData(cedeoutRateDataQuery);
					if(cedeoutRateDataEntity != null) {
						cedeoutRateDataDTO = BeanUtil.toBean(cedeoutRateDataEntity, CedeoutRateDataDTO.class);
						cacheService.setComCedeoutRateData(cedeoutRateDataDTO);
					}
				}
			}else if(CedeoutEnums.费率维护_折扣率.getValue() == rateType){
				String disRateSpecialRiskCode = DictUtils.getDictLabel(RsConstant.disRateSpecialRiskCode, dwsReinsuTradeDTO.getRiskCode());
				String disRateSpecialSelltype = DictUtils.getDictLabel(RsConstant.disRateSpecialSelltype, dwsReinsuTradeDTO.getSellType());
				if(StringUtils.isNotBlank(disRateSpecialRiskCode) && StringUtils.isNotBlank(disRateSpecialSelltype)) {
					cedeoutRateDataQuery.setSellType(dwsReinsuTradeDTO.getSellType());
				}else {
					cedeoutRateDataQuery.setSellType(RsConstant.ordinarySellType);
				}
				cedeoutRateDataQuery.setRateCode(dwsReinsuTradeDTO.getDisRateCode());
				cedeoutRateDataQuery.setInsuredSex(dwsReinsuTradeDTO.getInsuredSex() == CedeoutEnums.性别_男.getValue() ? CedeoutEnums.费率_性别_男.getValue() : CedeoutEnums.费率_性别_女.getValue());
				cedeoutRateDataDTO = cacheService.getDisCedeoutRateData(cedeoutRateDataQuery);
				if(cedeoutRateDataDTO == null) {
					CedeoutRateDataEntity cedeoutRateDataEntity = cedeoutRateDataMapper.selectCedeoutRateData(cedeoutRateDataQuery);
					if(cedeoutRateDataEntity != null) {
						cedeoutRateDataDTO = BeanUtil.toBean(cedeoutRateDataEntity, CedeoutRateDataDTO.class);
						cacheService.setDisCedeoutRateData(cedeoutRateDataDTO);
					}
				}
			}
		}catch(Exception e) {
			log.error("查询费率出错, cedeoutRateDataQuery:{}, 错误原因:", ReinsuJsonUtil.toJsonString(cedeoutRateDataQuery), e);
		}
		return cedeoutRateDataDTO;
	}
    
    
}
