package com.reinsurance.service.impl;

import java.math.BigDecimal;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.jd.lightning.common.annotation.DataSource;
import com.jd.lightning.common.core.domain.R;
import com.jd.lightning.common.enums.DataSourceType;
import com.reinsurance.dto.CedeoutRateDataDTO;
import com.reinsurance.dto.DwsReinsuTradeDTO;
import com.reinsurance.dto.ReservesFactorDTO;
import com.reinsurance.service.ICedeoutRateInfoService;
import com.reinsurance.service.IFormulaService;
import com.reinsurance.service.IReservesFactorService;
import com.reinsurance.service.ITrackBaseConfigService;

@Service
@DataSource(DataSourceType.MASTER)
public class TrackBaseConfigServiceImpl implements ITrackBaseConfigService {

	
	@Autowired
	private IFormulaService formulaService;
	
	@Autowired
	private IReservesFactorService reservesFactorService;
	
	@Autowired
	private ICedeoutRateInfoService cedeoutRateInfoService;
	
	@Override
	public ReservesFactorDTO selectReservesFactor(DwsReinsuTradeDTO dwsReinsuTradeDTO) {
		return reservesFactorService.selectReservesFactor(dwsReinsuTradeDTO);
	}

	@Override
	public CedeoutRateDataDTO selectCedeoutRateData(Integer rateType, DwsReinsuTradeDTO dwsReinsuTradeDTO) {
		return cedeoutRateInfoService.selectCedeoutRateData(rateType, dwsReinsuTradeDTO);
	}

	@Override
	public R<BigDecimal> getFormulaCalcResultValue(Integer formulaBigType, String formulaType, DwsReinsuTradeDTO dwsReinsuTradeDTO) {
		return formulaService.getFormulaCalcResultValue(formulaBigType, formulaType, dwsReinsuTradeDTO);
	}
	
}
