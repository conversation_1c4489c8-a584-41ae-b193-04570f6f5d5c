package com.reinsurance.service;

import com.reinsurance.query.DwsPrpProductQuery;
import com.jd.lightning.common.core.domain.Result;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 保单登记再保产品信息Service接口
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
public interface IDwsPrpProductService {
    
    /**
     * 查询保单登记再保产品信息
     *
     * @param id 保单登记再保产品信息主键
     * @return 保单登记再保产品信息
     */
    DwsPrpProductDTO selectDwsPrpProductById(Long id);

    /**
     * 查询保单登记再保产品信息列表
     *
     * @param dwsPrpProductQuery 保单登记再保产品信息
     * @return 保单登记再保产品信息集合
     */
    List<DwsPrpProductDTO> selectDwsPrpProductList(DwsPrpProductQuery dwsPrpProductQuery);

    /**
     * 新增保单登记再保产品信息
     *
     * @param dwsPrpProductDTO 保单登记再保产品信息
     * @return 结果
     */
    int insertDwsPrpProduct(DwsPrpProductDTO dwsPrpProductDTO);

    /**
     * 修改保单登记再保产品信息
     *
     * @param dwsPrpProductDTO 保单登记再保产品信息
     * @return 结果
     */
    int updateDwsPrpProduct(DwsPrpProductDTO dwsPrpProductDTO);

    /**
     * 批量删除保单登记再保产品信息
     *
     * @param ids 需要删除的保单登记再保产品信息主键集合
     * @return 结果
     */
    int deleteDwsPrpProductByIds(Long[] ids);

    /**
     * 删除保单登记再保产品信息信息
     *
     * @param id 保单登记再保产品信息主键
     * @return 结果
     */
    int deleteDwsPrpProductById(Long id);

    /**
     * 导入保单登记再保产品信息数据
     *
     * @param file 导入文件
     * @return 结果
     */
    Result importDwsPrpProduct(MultipartFile file);

    /**
     * 导出保单登记再保产品信息数据
     *
     * @param response 响应对象
     * @param dwsPrpProductQuery 查询条件
     */
    void exportDwsPrpProduct(HttpServletResponse response, DwsPrpProductQuery dwsPrpProductQuery);

    /**
     * 下载导入模板
     *
     * @param response 响应对象
     */
    void downloadTemplate(HttpServletResponse response);
}
