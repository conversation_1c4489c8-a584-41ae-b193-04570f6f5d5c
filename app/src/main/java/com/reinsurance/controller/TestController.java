package com.reinsurance.controller;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.common.utils.sign.Md5Utils;
import com.reinsurance.constant.RsConstant;
import com.reinsurance.domain.DwsContHistoryEntity;
import com.reinsurance.dto.DwsReinsuTradeDTO;
import com.reinsurance.dto.JobParamDTO;
import com.reinsurance.enums.CedeoutEnums;
import com.reinsurance.mapper.DwsContHistoryMapper;
import com.reinsurance.query.DwsReinsuTradeQuery;
import com.reinsurance.service.IDwsReinsuTradeService;
import com.reinsurance.task.TrackContHistoryJob;
import com.reinsurance.task.TrackProgrammeJob;
import com.reinsurance.utils.ReinsuJsonUtil;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;

@RestController
@RequestMapping("/test")
public class TestController extends BaseController {
	
	@Autowired
	TrackContHistoryJob trackContHistoryJob;
	
	@PostMapping("/trackContHistoryJob")
    public Result trackContHistoryJob(){
		JobParamDTO jobParam = new JobParamDTO();
		jobParam.setExecutor(this.getUsername());
        jobParam.setExecuteDate(DateUtils.getNowDate());
        CompletableFuture.runAsync(() -> {
        	trackContHistoryJob.handler(jobParam);
        });
        return Result.success("执行成功");
    }
	
	@Autowired
	TrackProgrammeJob trackProgrammeJob;
	
    @PostMapping("/trackProgrammeJob")
    public Result trackProgrammeJob(){
		JobParamDTO jobParam = new JobParamDTO();
		jobParam.setExecutor(this.getUsername());
        jobParam.setExecuteDate(DateUtils.getNowDate());
        CompletableFuture.runAsync(() -> {
        	trackProgrammeJob.handler(jobParam);
        });
        return Result.success("执行成功");
    }
    
    public static void main(String [] args) {
    	DwsReinsuTradeDTO reinsuTrade = new DwsReinsuTradeDTO();
    	reinsuTrade.setBillConfirmDate(DateUtils.getNowDate());
    	System.out.println(DateUtil.month(DateUtil.date()));
    }
    
    @Autowired
    private IDwsReinsuTradeService dwsReinsuTradeService;
    
    @PostMapping("/saveTrade")
    public Result saveTrade(@RequestBody DwsReinsuTradeDTO dwsReinsuTradeDTO) {
    	return Result.success(dwsReinsuTradeService.insertDwsReinsuTrade(dwsReinsuTradeDTO));
    }
    
    @PostMapping("/returnTrade")
    public Result returnTrade(@RequestBody DwsContHistoryEntity dwsContHistory) {
    	DwsReinsuTradeQuery dwsReinsuTradeQuery = new DwsReinsuTradeQuery();
        dwsReinsuTradeQuery.setContNo(dwsContHistory.getContNo());
        dwsReinsuTradeQuery.setStatus(CedeoutEnums.状态_有效.getValue());
        dwsReinsuTradeQuery.setDataType(CedeoutEnums.数据类型_分出.getValue());
        dwsReinsuTradeQuery.setBusiOccurDate(dwsContHistory.getBusiOccurDate());
        dwsReinsuTradeQuery.getParams().put("returnStatus", Arrays.asList(CedeoutEnums.摊回状态_未摊回.getValue()));
        dwsReinsuTradeQuery.getParams().put("calcStatus", Arrays.asList(CedeoutEnums.计算状态_成功.getValue(), CedeoutEnums.计算状态_忽略.getValue()));
        if (StrUtil.isNotBlank(dwsContHistory.getPolNo()) && !RsConstant.ALL_POLNO.equals(dwsContHistory.getPolNo())) {//险种号
        	dwsReinsuTradeQuery.setPolNo(dwsContHistory.getPolNo());
        }
        if (StrUtil.isNotBlank(dwsContHistory.getInsuredNo()) && !RsConstant.ALL_POLNO.equals(dwsContHistory.getInsuredNo())) {//被保险人号
        	dwsReinsuTradeQuery.setInsuredNo(dwsContHistory.getInsuredNo());
        }
		List<DwsReinsuTradeDTO> lastReinsuTradeList = dwsReinsuTradeService.selectWaitReturnOnCedeoutReinsuTrade(dwsReinsuTradeQuery);
    	return Result.success(lastReinsuTradeList);
    }
}
