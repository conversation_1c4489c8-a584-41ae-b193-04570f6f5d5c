package com.reinsurance.utils;

import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import lombok.extern.slf4j.Slf4j;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

@Slf4j
public class FormulaItemUtil {


    /**
     * 表达式
     * @param formulaValue
     * @param resultMap
     * @return
     */
    public static BigDecimal expressionExecute(String formulaValue, Map<String, Object> resultMap) {
    	BigDecimal formulaResult = BigDecimal.ZERO;
        try {
        	Expression compiledExp = AviatorEvaluator.compile(formulaValue, true);
        	if (compiledExp == null) {
                return formulaResult;
            }
        	Object expressionResult = compiledExp.execute(resultMap);
        	if(expressionResult instanceof Double) {
        		formulaResult = BigDecimal.valueOf((Double)expressionResult);
        	}else if (expressionResult instanceof BigDecimal) {
        		formulaResult = (BigDecimal)expressionResult;
        	}
        } catch (Exception e) {
            log.error("aviatorExpression-error, formulaValue = {}, data = {} 异常信息:{}", formulaValue, resultMap, e);
        }
        return formulaResult.setScale(2, RoundingMode.HALF_UP);
    }
}
