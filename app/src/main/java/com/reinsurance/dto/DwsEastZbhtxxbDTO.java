package com.reinsurance.dto;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.annotation.Excel.Type;
import com.jd.lightning.common.core.domain.BaseDTO;
import com.reinsurance.enums.BasicDataEnums.ReportPushStatus;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * East再保合同信息对象
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class DwsEastZbhtxxbDTO extends BaseDTO {
    
	private static final long serialVersionUID = -1258836232479576276L;

	/** 自增主键 */
    private Long id;

    /** 流水号 */
    @Excel(name = "流水号", type=Type.EXPORT)
    private String lsh;

    /** 保险机构代码（唯一固定值：000166） */
    @Excel(name = "保险机构代码")
    private String bxjgdm;

    /** 保险机构名称（唯一固定值：弘康人寿保险股份有限公司） */
    @Excel(name = "保险机构名称")
    private String bxjgmc;

    /** 再保险合同号码 */
    @Excel(name = "再保险合同号码")
    private String zbxhthm;

    /** 再保险合同名称 */
    @Excel(name = "再保险合同名称")
    private String zbxhtmc;

    /** 合同分类（唯一固定值：分出合同） */
    @Excel(name = "合同分类")
    private String htfl;

    /** 合同附约类型（合同,附约） */
    @Excel(name = "合同附约类型")
    private String htfylx;

    /** 再保险附约主合同号 */
    @Excel(name = "再保险附约主合同号")
    private String zbxfyzhth;

    /** 合同状态（有效,终止） */
    @Excel(name = "合同状态")
    private String htzt;

    /** 巨灾再保合同标志（0=否,1=是） */
    @Excel(name = "巨灾再保合同标志", dictType = "regulator_report_column_flag")
    private String jzzbhtbz;

    /** 临分合同标志（0=否,1=是） */
    @Excel(name = "临分合同标志", dictType = "regulator_report_column_flag")
    private String lfhtbz;

    /** 合同签署日期 */
    @Excel(name = "合同签署日期")
    private String htqsrq;

    /** 合同生效起期 */
    @Excel(name = "合同生效起期")
    private String htsxqq;

    /** 合同生效止期 */
    @Excel(name = "合同生效止期")
    private String htsxzq;

    /** 合同类型（唯一固定值：比例合同） */
    @Excel(name = "合同类型")
    private String htlx;

    /** 再保险公司代码 */
    @Excel(name = "再保险公司代码")
    private String zbxgsdm;

    /** 再保险公司名称 */
    @Excel(name = "再保险公司名称")
    private String zbxgsmc;

    /** 原保险公司代码（唯一固定值：000166） */
    @Excel(name = "原保险公司代码")
    private String ybxgsdm;

    /** 原保险公司名称（唯一固定值：弘康人寿保险股份有限公司） */
    @Excel(name = "原保险公司名称")
    private String ybxgsmc;

    /** 再保险经纪人 */
    @Excel(name = "再保险经纪人")
    private String zbxjjr;

    /** 采集日期 */
    @Excel(name = "采集日期")
    private String cjrq;
    
    /**数据报送批次号*/
    @Excel(name = "数据报送批次号", type=Type.EXPORT)
    private String sjbspch;
    
    /**管理机构*/
    @Excel(name = "管理机构", type=Type.EXPORT)
    private String managecom;

    /** 所属年份 */
    @Excel(name = "所属年份")
    private Integer reportYear;
    
    /** 所属月份 */
    @Excel(name = "所属月份")
    private Integer reportMonth;

    /** 所属账期 */
    private String accountPeriod;

    /** 数据来源（0=系统,1=人工） */
    @Excel(name = "数据来源", type=Type.EXPORT, dictType = "regulator_report_data_source")
    private Integer dataSource;

    /** 推送状态（0=未推送,1=已推送） */
    @Excel(name = "推送状态", type=Type.EXPORT, dictType = "regulator_report_push_status")
    private Integer pushStatus = ReportPushStatus.未推送.getCode();

    /** 推送日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "推送日期", type=Type.EXPORT, width = 30, dateFormat = "yyyy-MM-dd")
    private Date pushDate;

    /** 推送人 */
    @Excel(name = "推送人", type=Type.EXPORT)
    private String pushBy;

    /** 是否删除（0=未删除,1=已删除） */
    private Integer isDel;
}
