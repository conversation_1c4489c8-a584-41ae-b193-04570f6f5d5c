package com.reinsurance.dto.excel;

import cn.hutool.core.date.DatePattern;
import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * 再保虚拟合约
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class ExcelCedeoutVirtualContractDTO extends BaseDTO {

    private static final long serialVersionUID = 1L;

    @Excel(name = "版本号")
    private Long version;

    @Excel(name = "虚拟合同编码")
    private String virtualCode;

    @Excel(name = "虚拟合同名称")
    private String virtualName;

    @Excel(name = "签订日期", width = 40, dateFormat = DatePattern.NORM_DATETIME_PATTERN)
    private Date signDate;

    @Excel(name = "生效日期", width = 40, dateFormat = DatePattern.NORM_DATETIME_PATTERN)
    private Date effectiveDate;

    @Excel(name = "失效日期", width = 40, dateFormat = DatePattern.NORM_DATETIME_PATTERN)
    private Date expiredDate;

    @Excel(name = "状态", dictType = "re_virtual_status")
    private Integer status;

}
