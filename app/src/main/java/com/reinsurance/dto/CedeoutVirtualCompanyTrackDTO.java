package com.reinsurance.dto;

import java.math.BigDecimal;

import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 虚拟合同再保公司关系轨迹对象 t_cedeout_virtual_company_track
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class CedeoutVirtualCompanyTrackDTO extends BaseDTO
{
    private static final long serialVersionUID = 1L;

    /** 轨迹Id */
    private Long trackId;

    /** 业务表Id */
    private Long id;

    /** 批次号 */
    @Excel(name = "批次号")
    private String batchNo;

    /** 版本号 */
    @Excel(name = "版本号")
    private Long version;

    /** 虚拟合同编码 */
    @Excel(name = "虚拟合同编码")
    private String virtualCode;

    /** 再保公司编码 */
    @Excel(name = "再保公司编码")
    private String companyCode;

    /** 分保比例 */
    @Excel(name = "分保比例")
    private BigDecimal cedeoutScale;

    /** 状态（0=有效,1=无效） */
    @Excel(name = "状态", readConverterExp = "0=有效,1=无效")
    private Integer status;

    /** 是否删除（0=未删除,1=已删除） */
    @Excel(name = "是否删除", readConverterExp = "0=未删除,1=已删除")
    private Integer isDel;

}
