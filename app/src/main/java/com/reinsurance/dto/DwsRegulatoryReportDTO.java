package com.reinsurance.dto;

import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseDTO;
import com.reinsurance.enums.BasicDataEnums.ReportPushStatus;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 监管报表信息对象
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class DwsRegulatoryReportDTO extends BaseDTO {
    
	private static final long serialVersionUID = -1912503945318531723L;

	/** 自增主键 */
    private Long id;

    /** 报表类型编码 */
    private Integer typeCode;

    /** 报表类型名称 */
    @Excel(name = "报表类型名称")
    private String typeName;

    /** 报表编码 */
    private Integer reportCode;

    /** 报表名称 */
    @Excel(name = "报表名称")
    private String reportName;

    /** 所属年份 */
    @Excel(name = "所属年份")
    private Integer reportYear;

    /** 推送状态（0=未推送,1=部分推送,2=全部推送） */
    @Excel(name = "推送状态", readConverterExp = "0=未推送,1=部分推送,2=全部推送")
    private Integer pushStatus = ReportPushStatus.未推送.getCode();
    
    /** 是否删除（0=未删除,1=已删除） */
    private Integer isDel;
    
    /**报表月份*/
    private Integer reportMonth;
    
    /**账单类型（0=预提,1=实际）*/
    private Integer billType;

}
