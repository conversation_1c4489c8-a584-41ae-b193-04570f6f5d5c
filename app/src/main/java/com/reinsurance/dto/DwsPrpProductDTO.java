package com.reinsurance.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jd.lightning.common.core.domain.BaseDTO;
import com.jd.lightning.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.validation.constraints.*;
import java.util.Date;

/**
 * 保单登记再保产品信息DTO类
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class DwsPrpProductDTO extends BaseDTO {
    
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long Id;

    /** 交易编码 */
    @Excel(name = "交易编码")
    @NotBlank(message = "交易编码不能为空")
    @Size(max = 64, message = "交易编码长度不能超过64个字符")
    private String TransactionNo;

    /** 保险机构代码,唯一固定值000166 */
    @Excel(name = "保险机构代码")
    @NotBlank(message = "保险机构代码不能为空")
    @Size(max = 64, message = "保险机构代码长度不能超过64个字符")
    private String CompanyCode;

    /** 再保险合同号码 */
    @Excel(name = "再保险合同号码")
    @NotBlank(message = "再保险合同号码不能为空")
    @Size(max = 64, message = "再保险合同号码长度不能超过64个字符")
    private String ReInsuranceContNo;

    /** 再保险合同名称 */
    @Excel(name = "再保险合同名称")
    @NotBlank(message = "再保险合同名称不能为空")
    @Size(max = 256, message = "再保险合同名称长度不能超过256个字符")
    private String ReInsuranceContName;

    /** 再保险合同简称 */
    @Excel(name = "再保险合同简称")
    @NotBlank(message = "再保险合同简称不能为空")
    @Size(max = 256, message = "再保险合同简称长度不能超过256个字符")
    private String ReInsuranceContTitle;

    /** 再保险附约主合同号 */
    @Excel(name = "再保险附约主合同号")
    @NotBlank(message = "再保险附约主合同号不能为空")
    @Size(max = 64, message = "再保险附约主合同号长度不能超过64个字符")
    private String MainReInsuranceContNo;

    /** 合同附约类型,1:主合同,2:附约 */
    @Excel(name = "合同附约类型", readConverterExp = "1=主合同,2=附约")
    @NotBlank(message = "合同附约类型不能为空")
    @Size(max = 4, message = "合同附约类型长度不能超过4个字符")
    private String ContOrAmendmentType;

    /** 产品编码 */
    @Excel(name = "产品编码")
    @NotBlank(message = "产品编码不能为空")
    @Size(max = 64, message = "产品编码长度不能超过64个字符")
    private String ProductCode;

    /** 产品名称 */
    @Excel(name = "产品名称")
    @NotBlank(message = "产品名称不能为空")
    @Size(max = 128, message = "产品名称长度不能超过128个字符")
    private String ProductName;

    /** 团个性质,01:个险,02:团险,99:其他 */
    @Excel(name = "团个性质", readConverterExp = "01=个险,02=团险,99=其他")
    @NotBlank(message = "团个性质不能为空")
    @Size(max = 4, message = "团个性质长度不能超过4个字符")
    private String GPFlag;

    /** 险类代码 */
    @Excel(name = "险类代码")
    @NotBlank(message = "险类代码不能为空")
    @Size(max = 64, message = "险类代码长度不能超过64个字符")
    private String ProductType;

    /** 责任代码 */
    @Excel(name = "责任代码")
    @NotBlank(message = "责任代码不能为空")
    @Size(max = 64, message = "责任代码长度不能超过64个字符")
    private String LiabilityCode;

    /** 责任名称 */
    @Excel(name = "责任名称")
    @NotBlank(message = "责任名称不能为空")
    @Size(max = 128, message = "责任名称长度不能超过128个字符")
    private String LiabilityName;

    /** 再保险公司代码 */
    @Excel(name = "再保险公司代码")
    @NotBlank(message = "再保险公司代码不能为空")
    @Size(max = 64, message = "再保险公司代码长度不能超过64个字符")
    private String ReinsurerCode;

    /** 再保险公司名称 */
    @Excel(name = "再保险公司名称")
    @NotBlank(message = "再保险公司名称不能为空")
    @Size(max = 256, message = "再保险公司名称长度不能超过256个字符")
    private String ReinsurerName;

    /** 再保人参与份额比例 */
    @Excel(name = "再保人参与份额比例")
    @NotBlank(message = "再保人参与份额比例不能为空")
    @Size(max = 32, message = "再保人参与份额比例长度不能超过32个字符")
    private String ReinsuranceShare;

    /** 分保方式,1:溢额,2:成数,3:成数溢额混合,4:超赔 */
    @Excel(name = "分保方式", readConverterExp = "1=溢额,2=成数,3=成数溢额混合,4=超赔")
    @NotBlank(message = "分保方式不能为空")
    @Size(max = 4, message = "分保方式长度不能超过4个字符")
    private String ReinsurMode;

    /** 再保类型,01:事故超赔,02:修正共保方式,03:共保方式,04:风险保费方式,05:赔付率超赔,06:损失终止,07:险位超赔 */
    @Excel(name = "再保类型", readConverterExp = "01=事故超赔,02=修正共保方式,03=共保方式,04=风险保费方式,05=赔付率超赔,06=损失终止,07=险位超赔")
    @NotBlank(message = "再保类型不能为空")
    @Size(max = 4, message = "再保类型长度不能超过4个字符")
    private String ReInsuranceType;

    /** 保险期限类型,10:长期险,11:定期(年),12:定期(岁),13:定期(两可),14:终身,20:短期险,21:短期,22:极短期,30:主险缴费期,90:未知 */
    @Excel(name = "保险期限类型", readConverterExp = "10=长期险,11=定期(年),12=定期(岁),13=定期(两可),14=终身,20=短期险,21=短期,22=极短期,30=主险缴费期,90=未知")
    @NotBlank(message = "保险期限类型不能为空")
    @Size(max = 4, message = "保险期限类型长度不能超过4个字符")
    private String TermType;

    /** 自留额 */
    @Excel(name = "自留额")
    @NotBlank(message = "自留额不能为空")
    @Size(max = 32, message = "自留额长度不能超过32个字符")
    private String RetentionAmount;

    /** 自留比例 */
    @Excel(name = "自留比例")
    @NotBlank(message = "自留比例不能为空")
    @Size(max = 32, message = "自留比例长度不能超过32个字符")
    private String RetentionPercentage;

    /** 分保比例 */
    @Excel(name = "分保比例")
    @NotBlank(message = "分保比例不能为空")
    @Size(max = 32, message = "分保比例长度不能超过32个字符")
    private String QuotaSharePercentage;

    /** 所属年份 */
    @Excel(name = "所属年份")
    @NotNull(message = "所属年份不能为空")
    private Integer ReportYear;

    /** 所属月份 */
    @Excel(name = "所属月份")
    @NotNull(message = "所属月份不能为空")
    @Min(value = 1, message = "所属月份最小值为1")
    @Max(value = 12, message = "所属月份最大值为12")
    private Integer ReportMonth;

    /** 所属账期 */
    @Excel(name = "所属账期")
    @NotBlank(message = "所属账期不能为空")
    @Size(max = 64, message = "所属账期长度不能超过64个字符")
    private String AccountPeriod;

    /** 数据来源,0:系统,1:人工 */
    @Excel(name = "数据来源", readConverterExp = "0=系统,1=人工")
    @NotNull(message = "数据来源不能为空")
    private Integer DataSource;

    /** 推送状态,0:未推送,1:已推送 */
    @Excel(name = "推送状态", readConverterExp = "0=未推送,1=已推送")
    private Integer PushStatus;

    /** 推送日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "推送日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date PushDate;

    /** 推送人 */
    @Excel(name = "推送人")
    @Size(max = 64, message = "推送人长度不能超过64个字符")
    private String PushBy;
}
