<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.DwsPrpProductMapper">
    
    <resultMap type="DwsPrpProductEntity" id="DwsPrpProductResult">
        <result property="Id"    column="id"    />
        <result property="TransactionNo"    column="TransactionNo"    />
        <result property="CompanyCode"    column="CompanyCode"    />
        <result property="ReInsuranceContNo"    column="ReInsuranceContNo"    />
        <result property="ReInsuranceContName"    column="ReInsuranceContName"    />
        <result property="ReInsuranceContTitle"    column="ReInsuranceContTitle"    />
        <result property="MainReInsuranceContNo"    column="MainReInsuranceContNo"    />
        <result property="ContOrAmendmentType"    column="ContOrAmendmentType"    />
        <result property="ProductCode"    column="ProductCode"    />
        <result property="ProductName"    column="ProductName"    />
        <result property="GPFlag"    column="GPFlag"    />
        <result property="ProductType"    column="ProductType"    />
        <result property="LiabilityCode"    column="LiabilityCode"    />
        <result property="LiabilityName"    column="LiabilityName"    />
        <result property="ReinsurerCode"    column="ReinsurerCode"    />
        <result property="ReinsurerName"    column="ReinsurerName"    />
        <result property="ReinsuranceShare"    column="ReinsuranceShare"    />
        <result property="ReinsurMode"    column="ReinsurMode"    />
        <result property="ReInsuranceType"    column="ReInsuranceType"    />
        <result property="TermType"    column="TermType"    />
        <result property="RetentionAmount"    column="RetentionAmount"    />
        <result property="RetentionPercentage"    column="RetentionPercentage"    />
        <result property="QuotaSharePercentage"    column="QuotaSharePercentage"    />
        <result property="ReportYear"    column="report_year"    />
        <result property="ReportMonth"    column="report_month"    />
        <result property="AccountPeriod"    column="account_period"    />
        <result property="DataSource"    column="data_source"    />
        <result property="PushStatus"    column="push_status"    />
        <result property="PushDate"    column="push_date"    />
        <result property="PushBy"    column="push_by"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="IsDel"    column="is_del"    />
    </resultMap>

    <sql id="selectDwsPrpProductVo">
        select id, TransactionNo, CompanyCode, ReInsuranceContNo, ReInsuranceContName, ReInsuranceContTitle, MainReInsuranceContNo, ContOrAmendmentType, ProductCode, ProductName, GPFlag, ProductType, LiabilityCode, LiabilityName, ReinsurerCode, ReinsurerName, ReinsuranceShare, ReinsurMode, ReInsuranceType, TermType, RetentionAmount, RetentionPercentage, QuotaSharePercentage, report_year, report_month, account_period, data_source, push_status, push_date, push_by, create_by, create_time, update_by, update_time, is_del from t_dws_prp_product
    </sql>

    <select id="selectDwsPrpProductList" parameterType="DwsPrpProductQuery" resultMap="DwsPrpProductResult">
        <include refid="selectDwsPrpProductVo"/>
        <where>
            is_del = 0
            <if test="TransactionNo != null and TransactionNo != ''"> and TransactionNo like concat('%', #{TransactionNo}, '%')</if>
            <if test="CompanyCode != null and CompanyCode != ''"> and CompanyCode = #{CompanyCode}</if>
            <if test="ReInsuranceContNo != null and ReInsuranceContNo != ''"> and ReInsuranceContNo like concat('%', #{ReInsuranceContNo}, '%')</if>
            <if test="ReInsuranceContName != null and ReInsuranceContName != ''"> and ReInsuranceContName like concat('%', #{ReInsuranceContName}, '%')</if>
            <if test="ReInsuranceContTitle != null and ReInsuranceContTitle != ''"> and ReInsuranceContTitle like concat('%', #{ReInsuranceContTitle}, '%')</if>
            <if test="MainReInsuranceContNo != null and MainReInsuranceContNo != ''"> and MainReInsuranceContNo like concat('%', #{MainReInsuranceContNo}, '%')</if>
            <if test="ContOrAmendmentType != null and ContOrAmendmentType != ''"> and ContOrAmendmentType = #{ContOrAmendmentType}</if>
            <if test="ProductCode != null and ProductCode != ''"> and ProductCode like concat('%', #{ProductCode}, '%')</if>
            <if test="ProductName != null and ProductName != ''"> and ProductName like concat('%', #{ProductName}, '%')</if>
            <if test="GPFlag != null and GPFlag != ''"> and GPFlag = #{GPFlag}</if>
            <if test="ProductType != null and ProductType != ''"> and ProductType like concat('%', #{ProductType}, '%')</if>
            <if test="LiabilityCode != null and LiabilityCode != ''"> and LiabilityCode like concat('%', #{LiabilityCode}, '%')</if>
            <if test="LiabilityName != null and LiabilityName != ''"> and LiabilityName like concat('%', #{LiabilityName}, '%')</if>
            <if test="ReinsurerCode != null and ReinsurerCode != ''"> and ReinsurerCode like concat('%', #{ReinsurerCode}, '%')</if>
            <if test="ReinsurerName != null and ReinsurerName != ''"> and ReinsurerName like concat('%', #{ReinsurerName}, '%')</if>
            <if test="ReinsuranceShare != null and ReinsuranceShare != ''"> and ReinsuranceShare = #{ReinsuranceShare}</if>
            <if test="ReinsurMode != null and ReinsurMode != ''"> and ReinsurMode = #{ReinsurMode}</if>
            <if test="ReInsuranceType != null and ReInsuranceType != ''"> and ReInsuranceType = #{ReInsuranceType}</if>
            <if test="TermType != null and TermType != ''"> and TermType = #{TermType}</if>
            <if test="RetentionAmount != null and RetentionAmount != ''"> and RetentionAmount = #{RetentionAmount}</if>
            <if test="RetentionPercentage != null and RetentionPercentage != ''"> and RetentionPercentage = #{RetentionPercentage}</if>
            <if test="QuotaSharePercentage != null and QuotaSharePercentage != ''"> and QuotaSharePercentage = #{QuotaSharePercentage}</if>
            <if test="ReportYear != null"> and report_year = #{ReportYear}</if>
            <if test="ReportMonth != null"> and report_month = #{ReportMonth}</if>
            <if test="AccountPeriod != null and AccountPeriod != ''"> and account_period like concat('%', #{AccountPeriod}, '%')</if>
            <if test="DataSource != null"> and data_source = #{DataSource}</if>
            <if test="PushStatus != null"> and push_status = #{PushStatus}</if>
            <if test="PushDate != null"> and push_date = #{PushDate}</if>
            <if test="PushBy != null and PushBy != ''"> and push_by like concat('%', #{PushBy}, '%')</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectDwsPrpProductById" parameterType="Long" resultMap="DwsPrpProductResult">
        <include refid="selectDwsPrpProductVo"/>
        where id = #{id} and is_del = 0
    </select>
        
    <insert id="insertDwsPrpProduct" parameterType="DwsPrpProductEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_dws_prp_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="TransactionNo != null">TransactionNo,</if>
            <if test="CompanyCode != null">CompanyCode,</if>
            <if test="ReInsuranceContNo != null">ReInsuranceContNo,</if>
            <if test="ReInsuranceContName != null">ReInsuranceContName,</if>
            <if test="ReInsuranceContTitle != null">ReInsuranceContTitle,</if>
            <if test="MainReInsuranceContNo != null">MainReInsuranceContNo,</if>
            <if test="ContOrAmendmentType != null">ContOrAmendmentType,</if>
            <if test="ProductCode != null">ProductCode,</if>
            <if test="ProductName != null">ProductName,</if>
            <if test="GPFlag != null">GPFlag,</if>
            <if test="ProductType != null">ProductType,</if>
            <if test="LiabilityCode != null">LiabilityCode,</if>
            <if test="LiabilityName != null">LiabilityName,</if>
            <if test="ReinsurerCode != null">ReinsurerCode,</if>
            <if test="ReinsurerName != null">ReinsurerName,</if>
            <if test="ReinsuranceShare != null">ReinsuranceShare,</if>
            <if test="ReinsurMode != null">ReinsurMode,</if>
            <if test="ReInsuranceType != null">ReInsuranceType,</if>
            <if test="TermType != null">TermType,</if>
            <if test="RetentionAmount != null">RetentionAmount,</if>
            <if test="RetentionPercentage != null">RetentionPercentage,</if>
            <if test="QuotaSharePercentage != null">QuotaSharePercentage,</if>
            <if test="ReportYear != null">report_year,</if>
            <if test="ReportMonth != null">report_month,</if>
            <if test="AccountPeriod != null">account_period,</if>
            <if test="DataSource != null">data_source,</if>
            <if test="PushStatus != null">push_status,</if>
            <if test="PushDate != null">push_date,</if>
            <if test="PushBy != null">push_by,</if>
            <if test="createBy != null">create_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="TransactionNo != null">#{TransactionNo},</if>
            <if test="CompanyCode != null">#{CompanyCode},</if>
            <if test="ReInsuranceContNo != null">#{ReInsuranceContNo},</if>
            <if test="ReInsuranceContName != null">#{ReInsuranceContName},</if>
            <if test="ReInsuranceContTitle != null">#{ReInsuranceContTitle},</if>
            <if test="MainReInsuranceContNo != null">#{MainReInsuranceContNo},</if>
            <if test="ContOrAmendmentType != null">#{ContOrAmendmentType},</if>
            <if test="ProductCode != null">#{ProductCode},</if>
            <if test="ProductName != null">#{ProductName},</if>
            <if test="GPFlag != null">#{GPFlag},</if>
            <if test="ProductType != null">#{ProductType},</if>
            <if test="LiabilityCode != null">#{LiabilityCode},</if>
            <if test="LiabilityName != null">#{LiabilityName},</if>
            <if test="ReinsurerCode != null">#{ReinsurerCode},</if>
            <if test="ReinsurerName != null">#{ReinsurerName},</if>
            <if test="ReinsuranceShare != null">#{ReinsuranceShare},</if>
            <if test="ReinsurMode != null">#{ReinsurMode},</if>
            <if test="ReInsuranceType != null">#{ReInsuranceType},</if>
            <if test="TermType != null">#{TermType},</if>
            <if test="RetentionAmount != null">#{RetentionAmount},</if>
            <if test="RetentionPercentage != null">#{RetentionPercentage},</if>
            <if test="QuotaSharePercentage != null">#{QuotaSharePercentage},</if>
            <if test="ReportYear != null">#{ReportYear},</if>
            <if test="ReportMonth != null">#{ReportMonth},</if>
            <if test="AccountPeriod != null">#{AccountPeriod},</if>
            <if test="DataSource != null">#{DataSource},</if>
            <if test="PushStatus != null">#{PushStatus},</if>
            <if test="PushDate != null">#{PushDate},</if>
            <if test="PushBy != null">#{PushBy},</if>
            <if test="createBy != null">#{createBy},</if>
         </trim>
    </insert>

    <update id="updateDwsPrpProduct" parameterType="DwsPrpProductEntity">
        update t_dws_prp_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="TransactionNo != null">TransactionNo = #{TransactionNo},</if>
            <if test="CompanyCode != null">CompanyCode = #{CompanyCode},</if>
            <if test="ReInsuranceContNo != null">ReInsuranceContNo = #{ReInsuranceContNo},</if>
            <if test="ReInsuranceContName != null">ReInsuranceContName = #{ReInsuranceContName},</if>
            <if test="ReInsuranceContTitle != null">ReInsuranceContTitle = #{ReInsuranceContTitle},</if>
            <if test="MainReInsuranceContNo != null">MainReInsuranceContNo = #{MainReInsuranceContNo},</if>
            <if test="ContOrAmendmentType != null">ContOrAmendmentType = #{ContOrAmendmentType},</if>
            <if test="ProductCode != null">ProductCode = #{ProductCode},</if>
            <if test="ProductName != null">ProductName = #{ProductName},</if>
            <if test="GPFlag != null">GPFlag = #{GPFlag},</if>
            <if test="ProductType != null">ProductType = #{ProductType},</if>
            <if test="LiabilityCode != null">LiabilityCode = #{LiabilityCode},</if>
            <if test="LiabilityName != null">LiabilityName = #{LiabilityName},</if>
            <if test="ReinsurerCode != null">ReinsurerCode = #{ReinsurerCode},</if>
            <if test="ReinsurerName != null">ReinsurerName = #{ReinsurerName},</if>
            <if test="ReinsuranceShare != null">ReinsuranceShare = #{ReinsuranceShare},</if>
            <if test="ReinsurMode != null">ReinsurMode = #{ReinsurMode},</if>
            <if test="ReInsuranceType != null">ReInsuranceType = #{ReInsuranceType},</if>
            <if test="TermType != null">TermType = #{TermType},</if>
            <if test="RetentionAmount != null">RetentionAmount = #{RetentionAmount},</if>
            <if test="RetentionPercentage != null">RetentionPercentage = #{RetentionPercentage},</if>
            <if test="QuotaSharePercentage != null">QuotaSharePercentage = #{QuotaSharePercentage},</if>
            <if test="ReportYear != null">report_year = #{ReportYear},</if>
            <if test="ReportMonth != null">report_month = #{ReportMonth},</if>
            <if test="AccountPeriod != null">account_period = #{AccountPeriod},</if>
            <if test="DataSource != null">data_source = #{DataSource},</if>
            <if test="PushStatus != null">push_status = #{PushStatus},</if>
            <if test="PushDate != null">push_date = #{PushDate},</if>
            <if test="PushBy != null">push_by = #{PushBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{Id}
    </update>

    <update id="deleteDwsPrpProductById" parameterType="Long">
        update t_dws_prp_product set is_del = 1 where id = #{id}
    </update>

    <update id="deleteDwsPrpProductByIds" parameterType="String">
        update t_dws_prp_product set is_del = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <insert id="insertBatchDwsPrpProduct" parameterType="java.util.List">
        insert into t_dws_prp_product(TransactionNo, CompanyCode, ReInsuranceContNo, ReInsuranceContName, ReInsuranceContTitle, MainReInsuranceContNo, ContOrAmendmentType, ProductCode, ProductName, GPFlag, ProductType, LiabilityCode, LiabilityName, ReinsurerCode, ReinsurerName, ReinsuranceShare, ReinsurMode, ReInsuranceType, TermType, RetentionAmount, RetentionPercentage, QuotaSharePercentage, report_year, report_month, account_period, data_source, push_status, push_date, push_by, create_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.TransactionNo}, #{item.CompanyCode}, #{item.ReInsuranceContNo}, #{item.ReInsuranceContName}, #{item.ReInsuranceContTitle}, #{item.MainReInsuranceContNo}, #{item.ContOrAmendmentType}, #{item.ProductCode}, #{item.ProductName}, #{item.GPFlag}, #{item.ProductType}, #{item.LiabilityCode}, #{item.LiabilityName}, #{item.ReinsurerCode}, #{item.ReinsurerName}, #{item.ReinsuranceShare}, #{item.ReinsurMode}, #{item.ReInsuranceType}, #{item.TermType}, #{item.RetentionAmount}, #{item.RetentionPercentage}, #{item.QuotaSharePercentage}, #{item.ReportYear}, #{item.ReportMonth}, #{item.AccountPeriod}, #{item.DataSource}, #{item.PushStatus}, #{item.PushDate}, #{item.PushBy}, #{item.createBy})
        </foreach>
    </insert>
</mapper>
