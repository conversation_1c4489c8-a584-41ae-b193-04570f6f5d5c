<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.LiabilityFormulaTrackMapper">
    
    <resultMap type="LiabilityFormulaTrackEntity" id="LiabilityFormulaTrackResult">
        <result property="trackId"    column="track_id"    />
        <result property="id"    column="id"    />
        <result property="batchNo"    column="batch_no"    />
        <result property="version"    column="version"    />
        <result property="riskCode"    column="risk_code"    />
        <result property="riskName"    column="risk_name"    />
        <result property="liabilityCode"    column="liability_code"    />
        <result property="liabilityName"    column="liability_name"    />
        <result property="formulaType"    column="formula_type"    />
        <result property="formulaCode"    column="formula_code"    />
        <result property="formulaName"    column="formula_name"    />
        <result property="minAge"    column="min_age"    />
        <result property="maxAge"    column="max_age"    />
        <result property="minPolicyYear"    column="min_policy_year"    />
        <result property="maxPolicyYear"    column="max_policy_year"    />
        <result property="payendExpire"    column="payend_expire"    />
        <result property="supportPlan"    column="support_plan"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectLiabilityFormulaTrackVo">
        select track_id, id, batch_no, version, risk_code, risk_name, liability_code, liability_name, formula_type, formula_code, formula_name, min_age, max_age, min_policy_year, max_policy_year, payend_expire,support_plan, status, remark, is_del, create_by, create_time, update_by, update_time from t_liability_formula_track
    </sql>

    <select id="selectLiabilityFormulaTrackList" parameterType="LiabilityFormulaTrackQuery" resultMap="LiabilityFormulaTrackResult">
        <include refid="selectLiabilityFormulaTrackVo"/>
        <where>  
            <if test="batchNo != null  and batchNo != ''"> and batch_no = #{batchNo}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="riskCode != null  and riskCode != ''"> and risk_code = #{riskCode}</if>
            <if test="riskName != null  and riskName != ''"> and risk_name like concat('%', #{riskName}, '%')</if>
            <if test="liabilityCode != null  and liabilityCode != ''"> and liability_code = #{liabilityCode}</if>
            <if test="liabilityName != null  and liabilityName != ''"> and liability_name like concat('%', #{liabilityName}, '%')</if>
            <if test="formulaType != null and formulaType != ''"> and formula_type = #{formulaType}</if>
            <if test="formulaCode != null  and formulaCode != ''"> and formula_code = #{formulaCode}</if>
            <if test="formulaName != null  and formulaName != ''"> and formula_name like concat('%', #{formulaName}, '%')</if>
            <if test="minAge != null "> and min_age = #{minAge}</if>
            <if test="maxAge != null "> and max_age = #{maxAge}</if>
            <if test="minPolicyYear != null "> and min_policy_year = #{minPolicyYear}</if>
            <if test="maxPolicyYear != null "> and max_policy_year = #{maxPolicyYear}</if>
            <if test="payendExpire != null "> and payend_expire = #{payendExpire}</if>
            <if test="supportPlan != null "> and support_plan = #{supportPlan}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectLiabilityFormulaTrackByTrackId" parameterType="Long" resultMap="LiabilityFormulaTrackResult">
        <include refid="selectLiabilityFormulaTrackVo"/>
        where track_id = #{trackId}
    </select>
        
    <insert id="insertLiabilityFormulaTrack" parameterType="LiabilityFormulaTrackEntity" useGeneratedKeys="true" keyProperty="trackId">
        insert into t_liability_formula_track
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="batchNo != null and batchNo != ''">batch_no,</if>
            <if test="version != null">version,</if>
            <if test="riskCode != null and riskCode != ''">risk_code,</if>
            <if test="riskName != null and riskName != ''">risk_name,</if>
            <if test="liabilityCode != null and liabilityCode != ''">liability_code,</if>
            <if test="liabilityName != null and liabilityName != ''">liability_name,</if>
            <if test="formulaType != null and formulaType != ''">formula_type,</if>
            <if test="formulaCode != null and formulaCode != ''">formula_code,</if>
            <if test="formulaName != null and formulaName != ''">formula_name,</if>
            <if test="minAge != null">min_age,</if>
            <if test="maxAge != null">max_age,</if>
            <if test="minPolicyYear != null">min_policy_year,</if>
            <if test="maxPolicyYear != null">max_policy_year,</if>
            <if test="payendExpire != null">payend_expire,</if>
            <if test="supportPlan != null "> support_plan ,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="batchNo != null and batchNo != ''">#{batchNo},</if>
            <if test="version != null">#{version},</if>
            <if test="riskCode != null and riskCode != ''">#{riskCode},</if>
            <if test="riskName != null and riskName != ''">#{riskName},</if>
            <if test="liabilityCode != null and liabilityCode != ''">#{liabilityCode},</if>
            <if test="liabilityName != null and liabilityName != ''">#{liabilityName},</if>
            <if test="formulaType != null and formulaType != ''">#{formulaType},</if>
            <if test="formulaCode != null and formulaCode != ''">#{formulaCode},</if>
            <if test="formulaName != null and formulaName != ''">#{formulaName},</if>
            <if test="minAge != null">#{minAge},</if>
            <if test="maxAge != null">#{maxAge},</if>
            <if test="minPolicyYear != null">#{minPolicyYear},</if>
            <if test="maxPolicyYear != null">#{maxPolicyYear},</if>
            <if test="payendExpire != null">#{payendExpire},</if>
            <if test="supportPlan != null ">#{supportPlan},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateLiabilityFormulaTrack" parameterType="LiabilityFormulaTrackEntity">
        update t_liability_formula_track
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="batchNo != null and batchNo != ''">batch_no = #{batchNo},</if>
            <if test="version != null">version = #{version},</if>
            <if test="riskCode != null and riskCode != ''">risk_code = #{riskCode},</if>
            <if test="riskName != null and riskName != ''">risk_name = #{riskName},</if>
            <if test="liabilityCode != null and liabilityCode != ''">liability_code = #{liabilityCode},</if>
            <if test="liabilityName != null and liabilityName != ''">liability_name = #{liabilityName},</if>
            <if test="formulaType != null and formulaType != ''">formula_type = #{formulaType},</if>
            <if test="formulaCode != null and formulaCode != ''">formula_code = #{formulaCode},</if>
            <if test="formulaName != null and formulaName != ''">formula_name = #{formulaName},</if>
            <if test="minAge != null">min_age = #{minAge},</if>
            <if test="maxAge != null">max_age = #{maxAge},</if>
            <if test="minPolicyYear != null">min_policy_year = #{minPolicyYear},</if>
            <if test="maxPolicyYear != null">max_policy_year = #{maxPolicyYear},</if>
            <if test="payendExpire != null">payend_expire = #{payendExpire},</if>
            <if test="supportPlan != null "> support_plan = #{supportPlan},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where track_id = #{trackId}
    </update>

    <delete id="deleteLiabilityFormulaTrackByTrackId" parameterType="Long">
        delete from t_liability_formula_track where track_id = #{trackId}
    </delete>

    <delete id="deleteLiabilityFormulaTrackByTrackIds" parameterType="String">
        delete from t_liability_formula_track where track_id in 
        <foreach item="trackId" collection="array" open="(" separator="," close=")">
            #{trackId}
        </foreach>
    </delete>
</mapper>