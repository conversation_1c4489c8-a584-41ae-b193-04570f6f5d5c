<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.ImportDataLogDetailMapper">
    
    <resultMap type="ImportDataLogDetailEntity" id="ImportDataLogDetailResult">
        <result property="id"    column="id"    />
        <result property="batchNo"    column="batch_no"    />
        <result property="tableEn"    column="table_en"    />
        <result property="tableName"    column="table_name"    />
        <result property="dataStatus"    column="data_status"    />
        <result property="effectiveDate"    column="effective_date"    />
        <result property="remark"    column="remark"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectImportDataLogDetailVo">
        select id, batch_no, table_en, table_name, data_status, effective_date, remark, is_del, create_by, create_time, update_by, update_time from t_import_data_log_detail
    </sql>

    <select id="selectImportDataLogDetailList" parameterType="ImportDataLogDetailQuery" resultMap="ImportDataLogDetailResult">
        <include refid="selectImportDataLogDetailVo"/>
        <where>  
            <if test="batchNo != null  and batchNo != ''"> and batch_no = #{batchNo}</if>
            <if test="tableEn != null  and tableEn != ''"> and table_en = #{tableEn}</if>
            <if test="tableName != null  and tableName != ''"> and table_name like concat('%', #{tableName}, '%')</if>
            <if test="dataStatus != null "> and data_status = #{dataStatus}</if>
            <if test="effectiveDate != null "> and effective_date = #{effectiveDate}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectImportDataLogDetailById" parameterType="Long" resultMap="ImportDataLogDetailResult">
        <include refid="selectImportDataLogDetailVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertImportDataLogDetail" parameterType="ImportDataLogDetailEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_import_data_log_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">batch_no,</if>
            <if test="tableEn != null and tableEn != ''">table_en,</if>
            <if test="tableName != null and tableName != ''">table_name,</if>
            <if test="dataStatus != null">data_status,</if>
            <if test="effectiveDate != null">effective_date,</if>
            <if test="remark != null">remark,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">#{batchNo},</if>
            <if test="tableEn != null and tableEn != ''">#{tableEn},</if>
            <if test="tableName != null and tableName != ''">#{tableName},</if>
            <if test="dataStatus != null">#{dataStatus},</if>
            <if test="effectiveDate != null">#{effectiveDate},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateImportDataLogDetail" parameterType="ImportDataLogDetailEntity">
        update t_import_data_log_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">batch_no = #{batchNo},</if>
            <if test="tableEn != null and tableEn != ''">table_en = #{tableEn},</if>
            <if test="tableName != null and tableName != ''">table_name = #{tableName},</if>
            <if test="dataStatus != null">data_status = #{dataStatus},</if>
            <if test="effectiveDate != null">effective_date = #{effectiveDate},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteImportDataLogDetailById" parameterType="Long">
        delete from t_import_data_log_detail where id = #{id}
    </delete>

    <delete id="deleteImportDataLogDetailByIds" parameterType="String">
        delete from t_import_data_log_detail where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectImportDataLogDetailByBatchNos" parameterType="String" resultMap="ImportDataLogDetailResult">
        <include refid="selectImportDataLogDetailVo"/>
        where batch_no in
        <foreach item="batchNo" collection="batchNos" open="(" separator="," close=")">
            #{batchNo}
        </foreach>
    </select>

</mapper>