#starrocks	ddl
alter table t_dws_reinsu_trade add column cont_month tinyint(4) NULL DEFAULT "0" COMMENT "分出月份" after cont_year;
alter table t_dws_reinsu_policy_liability add column cont_month tinyint(4) NULL DEFAULT "0" COMMENT "分出月份" after cont_year, add column busi_occur_time datetime NULL COMMENT "业务发生时间" after busi_occur_date, add column unique_key varchar(128) NULL COMMENT "唯一键" after batch_no, add column tdch_ids varchar(128) NULL COMMENT "历史表id集合" after batch_no, add column busi_type tinyint(4) NULL COMMENT "业务类型(0=新单,1=续期)" after batch_no;

#starrocks	dml
update t_dws_reinsu_policy_liability set busi_occur_time = str_to_date(concat_ws(' ', if(cont_year>1, date_format(previous_cont_anniversary, '%Y-%m-%d'), date_format(cont_make_date, '%Y-%m-%d')), cont_make_time), '%Y-%m-%d %H:%i:%s') where busi_occur_time is null;
update t_dws_reinsu_policy_liability set unique_key=concat_ws('#', programme_code, pol_no, insured_no, liability_code, cont_year, cont_month) where unique_key is null;
update t_dws_reinsu_policy_liability set busi_type=if(cont_year>1, 1, 0) where busi_type is null;

#mysql	ddl
alter table t_report_template add column report_data_type tinyint(4) NULL COMMENT '报表数据类型(1=分出,2=保全,3=理赔)' after report_name;
alter table t_cedeout_programme add column start_busi_occur_date DATE NULL COMMENT '业务发生开始时间', add column end_busi_occur_date DATE NULL COMMENT '业务发生结束时间';
alter table t_cedeout_programme_track add column start_busi_occur_date DATE NULL COMMENT '业务发生开始时间', add column end_busi_occur_date DATE NULL COMMENT '业务发生结束时间';
alter table t_cedeout_programme_apply add column start_busi_occur_date DATE NULL COMMENT '业务发生开始时间', add column end_busi_occur_date DATE NULL COMMENT '业务发生结束时间';


#mysql	dml
