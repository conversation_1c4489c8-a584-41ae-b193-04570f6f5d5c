-- =============================================
-- 保单登记再保险数据表DDL
-- 数据库：StarRocks 3.1
-- 字符集：utf8
-- 生成时间：2025-06-23
-- =============================================

-- TB0001: 保单登记再保产品信息表
CREATE TABLE IF NOT EXISTS `t_dws_prp_product` (
    `Id` bigint NOT NULL COMMENT '主键',
    `TransactionNo` varchar(64) NOT NULL COMMENT '交易编码',
    `CompanyCode` varchar(64) NOT NULL COMMENT '保险机构代码,唯一固定值000166',
    `ReInsuranceContNo` varchar(64) NOT NULL COMMENT '再保险合同号码',
    `ReInsuranceContName` varchar(256) NOT NULL COMMENT '再保险合同名称',
    `ReInsuranceContTitle` varchar(256) NOT NULL COMMENT '再保险合同简称',
    `MainReInsuranceContNo` varchar(64) NOT NULL COMMENT '再保险附约主合同号',
    `ContOrAmendmentType` varchar(4) NOT NULL COMMENT '合同附约类型,1:主合同,2:附约',
    `ProductCode` varchar(64) NOT NULL COMMENT '产品编码',
    `ProductName` varchar(128) NOT NULL COMMENT '产品名称',
    `GPFlag` varchar(4) NOT NULL COMMENT '团个性质,01:个险,02:团险,99:其他',
    `ProductType` varchar(64) NOT NULL COMMENT '险类代码',
    `LiabilityCode` varchar(64) NOT NULL COMMENT '责任代码',
    `LiabilityName` varchar(128) NOT NULL COMMENT '责任名称',
    `ReinsurerCode` varchar(64) NOT NULL COMMENT '再保险公司代码',
    `ReinsurerName` varchar(256) NOT NULL COMMENT '再保险公司名称',
    `ReinsuranceShare` varchar(32) NOT NULL COMMENT '再保人参与份额比例',
    `ReinsurMode` varchar(4) NOT NULL COMMENT '分保方式,1:溢额,2:成数,3:成数溢额混合,4:超赔',
    `ReInsuranceType` varchar(4) NOT NULL COMMENT '再保类型,01:事故超赔,02:修正共保方式,03:共保方式,04:风险保费方式,05:赔付率超赔,06:损失终止,07:险位超赔',
    `TermType` varchar(4) NOT NULL COMMENT '保险期限类型,10:长期险,11:定期(年),12:定期(岁),13:定期(两可),14:终身,20:短期险,21:短期,22:极短期,30:主险缴费期,90:未知',
    `RetentionAmount` varchar(32) NOT NULL COMMENT '自留额',
    `RetentionPercentage` varchar(32) NOT NULL COMMENT '自留比例',
    `QuotaSharePercentage` varchar(32) NOT NULL COMMENT '分保比例',
    `ReportYear` int NOT NULL COMMENT '所属年份',
    `ReportMonth` tinyint NOT NULL COMMENT '所属月份',
    `AccountPeriod` varchar(64) NOT NULL COMMENT '所属账期',
    `DataSource` tinyint NOT NULL COMMENT '数据来源,0:系统,1:人工',
    `PushStatus` tinyint NOT NULL DEFAULT '0' COMMENT '推送状态,0:未推送,1:已推送',
    `PushDate` date NULL COMMENT '推送日期',
    `PushBy` varchar(64) NULL COMMENT '推送人',
    `Remark` varchar(128) NULL COMMENT "备注",
    `CreateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `CreateBy` varchar(64) NULL COMMENT '创建者',
    `UpdateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `UpdateBy` varchar(64) NULL COMMENT '更新者',
    `IsDel` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除, 0:否, 1:是'
) ENGINE=OLAP
DUPLICATE KEY(`Id`)
DISTRIBUTED BY HASH(`Id`) BUCKETS 10
PROPERTIES (
    "replication_num" = "3",
    "storage_format" = "DEFAULT"
)
COMMENT='保单登记再保产品信息表';