-- =============================================
-- 保单登记再保产品信息数据字典SQL
-- 生成时间：2025-06-24
-- =============================================

-- 团个性质字典
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) VALUES 
('团个性质', 'gp_flag', '0', 'admin', sysdate(), '团个性质字典类型');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES 
(1, '个险', '01', 'gp_flag', '', 'primary', 'Y', '0', 'admin', sysdate(), '个险'),
(2, '团险', '02', 'gp_flag', '', 'success', 'N', '0', 'admin', sysdate(), '团险'),
(3, '其他', '99', 'gp_flag', '', 'info', 'N', '0', 'admin', sysdate(), '其他');

-- 合同附约类型字典
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) VALUES 
('合同附约类型', 'cont_or_amendment_type', '0', 'admin', sysdate(), '合同附约类型字典类型');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES 
(1, '主合同', '1', 'cont_or_amendment_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '主合同'),
(2, '附约', '2', 'cont_or_amendment_type', '', 'success', 'N', '0', 'admin', sysdate(), '附约');

-- 分保方式字典
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) VALUES 
('分保方式', 'reinsur_mode', '0', 'admin', sysdate(), '分保方式字典类型');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES 
(1, '溢额', '1', 'reinsur_mode', '', 'primary', 'Y', '0', 'admin', sysdate(), '溢额'),
(2, '成数', '2', 'reinsur_mode', '', 'success', 'N', '0', 'admin', sysdate(), '成数'),
(3, '成数溢额混合', '3', 'reinsur_mode', '', 'info', 'N', '0', 'admin', sysdate(), '成数溢额混合'),
(4, '超赔', '4', 'reinsur_mode', '', 'warning', 'N', '0', 'admin', sysdate(), '超赔');

-- 再保类型字典
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) VALUES 
('再保类型', 're_insurance_type', '0', 'admin', sysdate(), '再保类型字典类型');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES 
(1, '事故超赔', '01', 're_insurance_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '事故超赔'),
(2, '修正共保方式', '02', 're_insurance_type', '', 'success', 'N', '0', 'admin', sysdate(), '修正共保方式'),
(3, '共保方式', '03', 're_insurance_type', '', 'info', 'N', '0', 'admin', sysdate(), '共保方式'),
(4, '风险保费方式', '04', 're_insurance_type', '', 'warning', 'N', '0', 'admin', sysdate(), '风险保费方式'),
(5, '赔付率超赔', '05', 're_insurance_type', '', 'danger', 'N', '0', 'admin', sysdate(), '赔付率超赔'),
(6, '损失终止', '06', 're_insurance_type', '', 'default', 'N', '0', 'admin', sysdate(), '损失终止'),
(7, '险位超赔', '07', 're_insurance_type', '', 'primary', 'N', '0', 'admin', sysdate(), '险位超赔');

-- 保险期限类型字典
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) VALUES 
('保险期限类型', 'term_type', '0', 'admin', sysdate(), '保险期限类型字典类型');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES 
(1, '长期险', '10', 'term_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '长期险'),
(2, '定期(年)', '11', 'term_type', '', 'success', 'N', '0', 'admin', sysdate(), '定期(年)'),
(3, '定期(岁)', '12', 'term_type', '', 'info', 'N', '0', 'admin', sysdate(), '定期(岁)'),
(4, '定期(两可)', '13', 'term_type', '', 'warning', 'N', '0', 'admin', sysdate(), '定期(两可)'),
(5, '终身', '14', 'term_type', '', 'danger', 'N', '0', 'admin', sysdate(), '终身'),
(6, '短期险', '20', 'term_type', '', 'default', 'N', '0', 'admin', sysdate(), '短期险'),
(7, '短期', '21', 'term_type', '', 'primary', 'N', '0', 'admin', sysdate(), '短期'),
(8, '极短期', '22', 'term_type', '', 'success', 'N', '0', 'admin', sysdate(), '极短期'),
(9, '主险缴费期', '30', 'term_type', '', 'info', 'N', '0', 'admin', sysdate(), '主险缴费期'),
(10, '未知', '90', 'term_type', '', 'warning', 'N', '0', 'admin', sysdate(), '未知');

-- 数据来源字典
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) VALUES 
('数据来源', 'data_source', '0', 'admin', sysdate(), '数据来源字典类型');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES 
(1, '系统', '0', 'data_source', '', 'primary', 'Y', '0', 'admin', sysdate(), '系统'),
(2, '人工', '1', 'data_source', '', 'success', 'N', '0', 'admin', sysdate(), '人工');

-- 推送状态字典
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) VALUES 
('推送状态', 'push_status', '0', 'admin', sysdate(), '推送状态字典类型');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES 
(1, '未推送', '0', 'push_status', '', 'warning', 'Y', '0', 'admin', sysdate(), '未推送'),
(2, '已推送', '1', 'push_status', '', 'success', 'N', '0', 'admin', sysdate(), '已推送');
