# 保单登记再保产品信息表生成功能使用指南

## 功能概述

根据program_design.md文档第4.1.3.1.3章节的要求，实现了保单登记再保产品信息表(TB0001)的自动生成功能。该功能从原有配置信息中查询数据，转换为标准的TB0001格式并批量入库。

## 实现逻辑

### 1. 触发条件
- 监管报表类型：中保信数据报送 (typeCode = 1)
- 报表名称：再保产品信息表 (reportCode = 1)

### 2. 执行步骤
1. **查询原有配置信息**：从t_cedeout_contract_liability等表查询指定时间范围内的配置数据
2. **数据验证**：检查查询结果是否为空
3. **参数获取**：从系统配置表获取保险机构代码
4. **流水号生成**：使用Redis批量生成唯一的交易编码
5. **数据转换**：将原有配置转换为TB0001格式
6. **批量入库**：将处理后的数据批量插入t_dws_prp_product表

### 3. 核心SQL查询
```sql
select 
    cc.company_code as reinsurerCode, 
    rc.company_name as reinsurerName, 
    l.contract_code as reInsuranceContNo, 
    cc.contract_name as reInsuranceContName, 
    cc.contract_abbr as reInsuranceContTitle, 
    cc.contract_type as contOrAmendmentType, 
    cc.main_contract_code as mainReInsuranceContNo,
    l.risk_code as productCode, 
    trl.risk_name as productName, 
    trl.sale_chnl as gpFlag, 
    trl.ins_product_type as productType, 
    l.liability_code as liabilityCode, 
    trl.liability_name as liabilityName, 
    l.cedeout_way as reinsurMode,
    (select group_concat(lp.period_type_name separator';') 
     from t_risk_liability_period lp 
     where lp.is_del=0 and lp.status=0 and lp.risk_code=l.risk_code and lp.liability_code=l.liability_code) as termType,
    l.retention_line as retentionAmount, 
    format(l.self_ratio * 100, 2) as retentionPercentage, 
    format(l.cedeout_ratio * 100, 2) as quotaSharePercentage, 
    format(l.self_amount * 100, 2) as reinsuranceShare, 
    cc.cedeout_type as reInsuranceType
from t_cedeout_contract_liability l 
inner join t_risk_liability trl on l.risk_code=trl.risk_code and trl.liability_code=l.liability_code 
inner join t_cedeout_contract cc on cc.contract_code=l.contract_code 
inner join t_cedeout_company rc on cc.company_code=rc.company_code
where l.is_del=0 and l.status=0 and trl.is_del=0 and trl.status=0 
and l.create_time >= date_format(#{startDate}, '%Y-%m-%d') 
and l.create_time <= date_format(#{endDate}, '%Y-%m-%d') 
order by l.id
```

## 使用方式

### 1. 通过监管报表界面
1. 登录系统，进入监管报表管理页面
2. 选择报表类型：中保信数据报送
3. 选择报表名称：再保产品信息表
4. 选择报表年份和月份
5. 点击生成按钮

### 2. 直接API调用（测试用）
```http
POST /dws/prp/product/generate
Content-Type: application/x-www-form-urlencoded

startDate=2025-01-01&endDate=2025-01-31
```

## 前置条件

### 1. 系统配置
- 需要在系统参数表中配置`eastReportInsCompanyCode`参数（保险机构代码）

### 2. 基础数据
- t_cedeout_contract_liability表中需要有有效的合同责任数据
- t_risk_liability表中需要有对应的险种责任数据
- t_cedeout_contract表中需要有对应的合同数据
- t_cedeout_company表中需要有对应的再保公司数据

### 3. Redis服务
- Redis服务需要正常运行，用于生成唯一流水号

## 权限配置

需要以下权限：
- `dws:prp:product:generate` - 生成数据权限
- `dws:prp:product:list` - 查看列表权限
- `dws:prp:product:query` - 查询详情权限

## 字段映射说明

| TB0001字段 | 来源表.字段 | 说明 |
|-----------|------------|------|
| ReinsurerCode | t_cedeout_contract.company_code | 再保险公司代码 |
| ReinsurerName | t_cedeout_company.company_name | 再保险公司名称 |
| ReInsuranceContNo | t_cedeout_contract_liability.contract_code | 再保险合同号码 |
| ReInsuranceContName | t_cedeout_contract.contract_name | 再保险合同名称 |
| ReInsuranceContTitle | t_cedeout_contract.contract_abbr | 再保险合同简称 |
| ContOrAmendmentType | t_cedeout_contract.contract_type | 合同附约类型 |
| MainReInsuranceContNo | t_cedeout_contract.main_contract_code | 再保险附约主合同号 |
| ProductCode | t_cedeout_contract_liability.risk_code | 产品编码 |
| ProductName | t_risk_liability.risk_name | 产品名称 |
| GPFlag | t_risk_liability.sale_chnl | 团个性质 |
| ProductType | t_risk_liability.ins_product_type | 险类代码 |
| LiabilityCode | t_cedeout_contract_liability.liability_code | 责任代码 |
| LiabilityName | t_risk_liability.liability_name | 责任名称 |
| ReinsurMode | t_cedeout_contract_liability.cedeout_way | 分保方式 |
| TermType | t_risk_liability_period.period_type_name | 保险期限类型 |
| RetentionAmount | t_cedeout_contract_liability.retention_line | 自留额 |
| RetentionPercentage | t_cedeout_contract_liability.self_ratio * 100 | 自留比例 |
| QuotaSharePercentage | t_cedeout_contract_liability.cedeout_ratio * 100 | 分保比例 |
| ReinsuranceShare | t_cedeout_contract_liability.self_amount * 100 | 再保人参与份额比例 |
| ReInsuranceType | t_cedeout_contract.cedeout_type | 再保类型 |

## 注意事项

1. **数据时间范围**：查询条件基于t_cedeout_contract_liability表的create_time字段
2. **数据状态**：只查询is_del=0且status=0的有效数据
3. **流水号唯一性**：使用Redis确保交易编码的唯一性
4. **批量处理**：支持大批量数据的高效处理
5. **异常处理**：完整的异常处理机制，确保数据一致性

## 故障排查

### 1. 生成失败常见原因
- 系统参数未配置保险机构代码
- Redis服务不可用
- 基础数据缺失或状态异常
- 数据库连接异常

### 2. 数据验证
- 检查生成的数据是否完整
- 验证交易编码是否唯一
- 确认字段映射是否正确
- 检查报表年月和账期是否正确

## 版本信息

- 实现版本：v1.0
- 最后更新：2025-06-19
- 对应设计文档：program_design.md 第4.1.3.1.3章节
